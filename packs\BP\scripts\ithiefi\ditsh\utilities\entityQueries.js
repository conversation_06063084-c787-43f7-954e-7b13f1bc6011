export const excludedEntities = {
    excludeTypes: [
        "minecraft:xp_orb",
        "minecraft:item",
        "minecraft:arrow",
        "minecraft:dragon_fireball",
        "minecraft:egg",
        "minecraft:ender_pearl",
        "minecraft:fireball",
        "minecraft:fishing_hook",
        "minecraft:lingering_potion",
        "minecraft:llama_spit",
        "minecraft:small_fireball",
        "minecraft:snowball",
        "minecraft:splash_potion",
        "minecraft:thrown_trident",
        "minecraft:wither_skull",
        "minecraft:wither_skull_dangerous",
        "minecraft:boat",
        "minecraft:chest_boat",
        "minecraft:minecart",
        "minecraft:chest_minecart",
        "minecraft:hopper_minecart",
        "minecraft:tnt_minecart",
        "minecraft:furnace_minecart",
        "minecraft:command_block_minecart",
        "minecraft:falling_block",
        "minecraft:armor_stand",
        "minecraft:painting",
        "minecraft:leash_knot",
        "minecraft:item_frame",
        "minecraft:glow_item_frame",
        "minecraft:ender_crystal",
        "minecraft:eye_of_ender_signal",
        "minecraft:evocation_fang",
        "minecraft:fireworks_rocket",
        "minecraft:shulker_bullet",
        "minecraft:area_effect_cloud",
        "minecraft:lightning_bolt",
        "minecraft:allay",
        "minecraft:axolotl",
        "minecraft:bat",
        "minecraft:cat",
        "minecraft:chicken",
        "minecraft:cod",
        "minecraft:cow",
        "minecraft:dolphin",
        "minecraft:donkey",
        "minecraft:fox",
        "minecraft:frog",
        "minecraft:glow_squid",
        "minecraft:goat",
        "minecraft:horse",
        "minecraft:llama",
        "minecraft:mooshroom",
        "minecraft:mule",
        "minecraft:ocelot",
        "minecraft:parrot",
        "minecraft:pig",
        "minecraft:polar_bear",
        "minecraft:pufferfish",
        "minecraft:rabbit",
        "minecraft:salmon",
        "minecraft:sheep",
        "minecraft:skeleton_horse",
        "minecraft:snow_golem",
        "minecraft:squid",
        "minecraft:strider",
        "minecraft:tropical_fish",
        "minecraft:turtle",
        "minecraft:villager",
        "minecraft:wandering_trader",
        "minecraft:wolf",
        "minecraft:bee",
        "minecraft:dolphin",
        "minecraft:goat",
        "minecraft:iron_golem",
        "minecraft:llama",
        "minecraft:panda",
        "minecraft:polar_bear",
        "minecraft:trader_llama",
        "minecraft:wolf",
        "minecraft:zombified_piglin",
        "minecraft:bogged",
        "minecraft:blaze",
        "minecraft:breeze",
        "minecraft:cave_spider",
        "minecraft:creeper",
        "minecraft:drowned",
        "minecraft:elder_guardian",
        "minecraft:enderman",
        "minecraft:endermite",
        "minecraft:evoker",
        "minecraft:ghast",
        "minecraft:guardian",
        "minecraft:hoglin",
        "minecraft:husk",
        "minecraft:magma_cube",
        "minecraft:phantom",
        "minecraft:piglin",
        "minecraft:piglin_brute",
        "minecraft:pillager",
        "minecraft:ravager",
        "minecraft:shulker",
        "minecraft:silverfish",
        "minecraft:skeleton",
        "minecraft:slime",
        "minecraft:spider",
        "minecraft:stray",
        "minecraft:vex",
        "minecraft:vindicator",
        "minecraft:witch",
        "minecraft:wither_skeleton",
        "minecraft:zoglin",
        "minecraft:zombie",
        "minecraft:zombie_villager",
        "minecraft:zombified_piglin"
    ],
    excludeFamilies: [
        "inanimate",
        "arrow",
        "boat",
        "minecart"
    ]
};
export const excludedEntitiesWithPlayer = {
    excludeTypes: [
        "minecraft:player",
        "minecraft:xp_orb",
        "minecraft:item",
        "minecraft:arrow",
        "minecraft:dragon_fireball",
        "minecraft:egg",
        "minecraft:ender_pearl",
        "minecraft:fireball",
        "minecraft:fishing_hook",
        "minecraft:lingering_potion",
        "minecraft:llama_spit",
        "minecraft:small_fireball",
        "minecraft:snowball",
        "minecraft:splash_potion",
        "minecraft:thrown_trident",
        "minecraft:wither_skull",
        "minecraft:wither_skull_dangerous",
        "minecraft:boat",
        "minecraft:chest_boat",
        "minecraft:minecart",
        "minecraft:chest_minecart",
        "minecraft:hopper_minecart",
        "minecraft:tnt_minecart",
        "minecraft:furnace_minecart",
        "minecraft:command_block_minecart",
        "minecraft:falling_block",
        "minecraft:armor_stand",
        "minecraft:painting",
        "minecraft:leash_knot",
        "minecraft:item_frame",
        "minecraft:glow_item_frame",
        "minecraft:ender_crystal",
        "minecraft:eye_of_ender_signal",
        "minecraft:evocation_fang",
        "minecraft:fireworks_rocket",
        "minecraft:shulker_bullet",
        "minecraft:area_effect_cloud",
        "minecraft:lightning_bolt",
        "minecraft:allay",
        "minecraft:axolotl",
        "minecraft:bat",
        "minecraft:cat",
        "minecraft:chicken",
        "minecraft:cod",
        "minecraft:cow",
        "minecraft:dolphin",
        "minecraft:donkey",
        "minecraft:fox",
        "minecraft:frog",
        "minecraft:glow_squid",
        "minecraft:goat",
        "minecraft:horse",
        "minecraft:llama",
        "minecraft:mooshroom",
        "minecraft:mule",
        "minecraft:ocelot",
        "minecraft:parrot",
        "minecraft:pig",
        "minecraft:polar_bear",
        "minecraft:pufferfish",
        "minecraft:rabbit",
        "minecraft:salmon",
        "minecraft:sheep",
        "minecraft:skeleton_horse",
        "minecraft:snow_golem",
        "minecraft:squid",
        "minecraft:strider",
        "minecraft:tropical_fish",
        "minecraft:turtle",
        "minecraft:villager",
        "minecraft:wandering_trader",
        "minecraft:wolf",
        "minecraft:bee",
        "minecraft:dolphin",
        "minecraft:goat",
        "minecraft:iron_golem",
        "minecraft:llama",
        "minecraft:panda",
        "minecraft:polar_bear",
        "minecraft:trader_llama",
        "minecraft:wolf",
        "minecraft:zombified_piglin",
        "minecraft:bogged",
        "minecraft:blaze",
        "minecraft:breeze",
        "minecraft:cave_spider",
        "minecraft:creeper",
        "minecraft:drowned",
        "minecraft:elder_guardian",
        "minecraft:enderman",
        "minecraft:endermite",
        "minecraft:evoker",
        "minecraft:ghast",
        "minecraft:guardian",
        "minecraft:hoglin",
        "minecraft:husk",
        "minecraft:magma_cube",
        "minecraft:phantom",
        "minecraft:piglin",
        "minecraft:piglin_brute",
        "minecraft:pillager",
        "minecraft:ravager",
        "minecraft:shulker",
        "minecraft:silverfish",
        "minecraft:skeleton",
        "minecraft:slime",
        "minecraft:spider",
        "minecraft:stray",
        "minecraft:vex",
        "minecraft:vindicator",
        "minecraft:witch",
        "minecraft:wither_skeleton",
        "minecraft:zoglin",
        "minecraft:zombie",
        "minecraft:zombie_villager",
        "minecraft:zombified_piglin"
    ],
    excludeFamilies: [
        "inanimate",
        "arrow",
        "boat",
        "minecart"
    ]
};
export const excludedEntitiesPlayerForMonsters = { families: ["monster"] };
export const excludedEntitiesPlayerForMobs = { families: ["mob"] };
export const excludedEntitiesPlayerForAll = {
    excludeTypes: [
        "minecraft:xp_orb",
        "minecraft:item",
        "minecraft:arrow",
        "minecraft:dragon_fireball",
        "minecraft:egg",
        "minecraft:ender_pearl",
        "minecraft:fireball",
        "minecraft:fishing_hook",
        "minecraft:lingering_potion",
        "minecraft:llama_spit",
        "minecraft:small_fireball",
        "minecraft:snowball",
        "minecraft:splash_potion",
        "minecraft:thrown_trident",
        "minecraft:wither_skull",
        "minecraft:wither_skull_dangerous",
        "minecraft:boat",
        "minecraft:chest_boat",
        "minecraft:minecart",
        "minecraft:chest_minecart",
        "minecraft:hopper_minecart",
        "minecraft:tnt_minecart",
        "minecraft:furnace_minecart",
        "minecraft:command_block_minecart",
        "minecraft:falling_block",
        "minecraft:armor_stand",
        "minecraft:painting",
        "minecraft:leash_knot",
        "minecraft:item_frame",
        "minecraft:glow_item_frame",
        "minecraft:ender_crystal",
        "minecraft:eye_of_ender_signal",
        "minecraft:evocation_fang",
        "minecraft:fireworks_rocket",
        "minecraft:shulker_bullet",
        "minecraft:area_effect_cloud",
        "minecraft:lightning_bolt"
    ],
    excludeFamilies: ["inanimate", "arrow", "boat", "minecart"]
};
