/**
 * All possible MinecraftPotionEffectTypes
 */
export declare enum MinecraftPotionEffectTypes {
    FireResistance = "FireResistance",
    Harming = "Harming",
    Healing = "Healing",
    Infested = "Infested",
    Invisibility = "Invisibility",
    Leaping = "Leaping",
    NightVision = "NightVision",
    None = "None",
    Oozing = "Oozing",
    Poison = "Poison",
    SlowFalling = "SlowFalling",
    Slowing = "Slowing",
    Strength = "Strength",
    Swiftness = "Swiftness",
    TurtleMaster = "TurtleMaster",
    WaterBreath = "WaterBreath",
    Weakness = "Weakness",
    Weaving = "Weaving",
    WindCharged = "WindCharged",
    Wither = "Wither"
}
/**
 * Union type equivalent of the MinecraftPotionEffectTypes enum.
 */
export type MinecraftPotionEffectTypesUnion = keyof typeof MinecraftPotionEffectTypes;
