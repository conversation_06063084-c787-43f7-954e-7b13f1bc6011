{"format_version": "1.19.0", "animation_controllers": {"controller.animation.ithiefi_ditsh_sonic.general": {"states": {"default": {"animations": ["idle"], "transitions": [{"walking": "q.ground_speed > 0.3"}, {"attack": "v.attack_time > 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "walking": {"animations": ["move"], "transitions": [{"default": "q.ground_speed < 0.3"}, {"attack": "v.attack_time > 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "attack": {"animations": ["attack"], "transitions": [{"default": "q.ground_speed < 0.3 && v.attack_time <= 0"}, {"walking": "q.ground_speed > 0.3 && v.attack_time <= 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}, "controller.animation.ithiefi_ditsh_sonic.move": {"initial_state": "walking", "states": {"walking": {"animations": ["walk"], "transitions": [{"running": "q.has_target"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "running": {"animations": ["run"], "transitions": [{"walking": "!q.has_target"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}}}