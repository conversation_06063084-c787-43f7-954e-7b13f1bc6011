{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:scp049", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 11, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}, "ditsh:has_target": {"minecraft:angry": {"angry_sound": "angry", "duration": 25, "duration_delta": 0, "sound_interval": [2, 5], "calm_event": {"event": "ditsh:on_calm", "target": "self"}}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:on_death": {}, "ditsh:maintain_chase_music": {}, "ditsh:has_target": {"add": {"component_groups": ["ditsh:has_target"]}}, "ditsh:on_calm": {"remove": {"component_groups": ["ditsh:has_target"]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "scp049", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.6, "height": 1.8}, "minecraft:health": {"value": 50, "max": 50}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.2}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 10}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.2}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_death": {"event": "ditsh:on_death", "target": "self"}, "minecraft:on_target_acquired": {"event": "ditsh:has_target", "target": "self"}, "minecraft:on_target_escape": {"event": "ditsh:on_calm", "target": "self"}}}}