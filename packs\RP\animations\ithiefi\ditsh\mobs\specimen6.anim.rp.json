{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_specimen6.idle": {"loop": true, "animation_length": 3.5834, "bones": {"head": {"rotation": ["math.sin(100*q.anim_time-20) * 2", 0, 0]}, "rightArm": {"rotation": [0, 0, "3-math.sin(100*q.anim_time) * 2"], "position": [0, "math.sin(100*q.anim_time-80) * 0.3", 0]}, "leftArm": {"rotation": [0, 0, "-3+math.sin(100*q.anim_time) * 2"], "position": [0, "math.sin(100*q.anim_time-80) * 0.3", 0]}, "torso": {"position": [0, "math.sin(100*q.anim_time-120) * 0.2", 0]}}}, "animation.ithiefi_ditsh_specimen6.walk": {"loop": true, "animation_length": 0.8959, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 2.4))", "bones": {"rightArm": {"rotation": ["math.cos(400*q.anim_time) * 20", 0, 0]}, "leftArm": {"rotation": ["-math.cos(400*q.anim_time) * 20", 0, 0]}, "rightLeg": {"rotation": ["-math.cos(400*q.anim_time) * 20", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time* 400) * 1.5,0 , 6)", 0]}, "leftLeg": {"rotation": ["math.cos(400*q.anim_time) * 20", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time* 400) * 1.5,0 , 6)", 0]}, "root": {"position": [0, "math.sin(800*q.anim_time-80) * 0.5", 0]}, "torso": {"rotation": ["math.cos(q.anim_time * 800) * 0.5", 0, 0], "position": [0, "math.sin(q.anim_time * 800) * 0.2", 0]}, "head": {"rotation": ["math.cos(800*q.anim_time-80) * 3", 0, 0]}}}, "animation.ithiefi_ditsh_specimen6.attack": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"rightArm": {"rotation": {"0.0": [-40, 15, 15], "0.0417": [-75, 10, 0], "0.125": [-160, 5, -15], "0.1667": [-200, -10, -30], "0.25": [-45, 0, -60], "0.2917": [-35, 0, -60], "0.375": [-40, 15, 15]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -2], "0.2917": [0, 0, -1], "0.375": [0, 0, 0]}}, "leftArm": {"rotation": {"0.0": [-40, 15, 15], "0.0417": [-75, -10, 0], "0.125": [-160, -5, 15], "0.1667": [-200, 10, 30], "0.25": [-45, 0, 60], "0.2917": [-35, 0, 60], "0.375": [-40, -15, -15]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -2], "0.2917": [0, 0, -1], "0.375": [0, 0, 0]}}, "torso": {"rotation": {"0.0": [2, 0, 0], "0.0417": [6, 0, 0], "0.125": [2, 4, 0], "0.1667": [0, 4, 0], "0.25": [18, -7, 0], "0.2917": [25, -22, -8], "0.375": [12, -22, -8], "0.4167": [6, -8, 0], "0.4583": [2, 0, 0]}}, "head": {"rotation": {"0.0": [2, 0, 0], "0.0417": [2, 0, 0], "0.125": [8, -4, 0], "0.1667": [8, -4, 0], "0.25": [-10, 6, 0], "0.2917": [-10, 20, -2], "0.375": [3, 22, 3], "0.4167": [2, 8, -2], "0.4583": [2, 0, 0]}}}}}}