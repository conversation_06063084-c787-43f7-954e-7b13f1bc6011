export function fixedPosRaycast(from, to, distance, step) {
    const positions = [];
    for (let i = 0; i <= distance; i += step) {
        const t = i / distance;
        const pos = {
            x: from.x + t * (to.x - from.x),
            y: from.y + t * (to.y - from.y),
            z: from.z + t * (to.z - from.z)
        };
        try {
            positions.push(pos);
        }
        catch (e) { }
    }
    return positions;
}
export function fixedLenRaycast(from, direction, length, step) {
    const positions = [];
    for (let i = 0; i <= length; i += step) {
        const particleLoc = {
            x: from.x + direction.x * i,
            y: from.y + direction.y * i,
            z: from.z + direction.z * i
        };
        try {
            positions.push(particleLoc);
        }
        catch (e) {
            continue;
        }
    }
    return positions;
}
