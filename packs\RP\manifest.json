{
  "format_version": 2,
  "metadata": { "authors": ["Raboy13"], "product_type": "addon" },
  "capabilities": ["script_eval"],
  "header": {
    "name": "pack.name",
    "description": "pack.description",
    "min_engine_version": [1, 21, 90],
    "uuid": "1280cebc-fa92-4902-9e82-bdbf8c570272",
    "version": [1, 0, 0],
    "base_game_version": [1, 21, 90],
    "pack_scope": "world"
  },
  "modules": [{ "type": "resources", "uuid": "09fc5b21-6d41-4ede-a647-25a00c0e6410", "version": [1, 0, 0] }],
  "dependencies": [
    // Behavior Pack
    { "uuid": "776f0f19-2845-4883-892d-3a8247cc61df", "version": [1, 0, 0] }
  ]
}
