{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:specimen6", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}, "ditsh:teleport_cooldown": {"type": "bool", "client_sync": false, "default": false}}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 131, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}, "ditsh:teleport_ready": {"minecraft:environment_sensor": {"triggers": [{"filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "target_distance", "subject": "other", "operator": ">", "value": 8}]}, "event": "ditsh:teleport_behind_player"}, {"filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "target_distance", "subject": "other", "operator": ">", "value": 64}]}, "event": "ditsh:teleport_to_idle_player"}]}}, "ditsh:teleport_cooldown_active": {"minecraft:behavior.timer_flag_2": {"priority": 9, "cooldown_range": 0, "duration_range": 3, "on_end": {"event": "ditsh:reset_teleport_cooldown", "target": "self"}}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:teleport_ready"]}, "queue_command": {"command": "playsound mob.ditsh.specimen6.spawn @a ~ ~ ~"}}, "ditsh:on_death": {}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}, "ditsh:teleport_behind_player": {"add": {"component_groups": ["ditsh:teleport_cooldown_active"]}, "remove": {"component_groups": ["ditsh:teleport_ready"]}, "set_property": {"ditsh:teleport_cooldown": true}}, "ditsh:teleport_to_idle_player": {"add": {"component_groups": ["ditsh:teleport_cooldown_active"]}, "remove": {"component_groups": ["ditsh:teleport_ready"]}, "set_property": {"ditsh:teleport_cooldown": true}}, "ditsh:reset_teleport_cooldown": {"add": {"component_groups": ["ditsh:teleport_ready"]}, "remove": {"component_groups": ["ditsh:teleport_cooldown_active"]}, "set_property": {"ditsh:teleport_cooldown": false}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 15 + (query.equipment_count * Math.Random(1,4)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "specimen6", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.4, "height": 2.9}, "minecraft:health": {"value": 60, "max": 60}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.0}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 10}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 1.2, "cooldown_time": 2, "can_spread_on_fire": true, "speed_multiplier": 1.0}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 128}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 128}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:follow_range": {"value": 128, "max": 128}, "minecraft:jump.static": {}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_target_acquired": {"event": "ditsh:start_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}}, "minecraft:on_target_escape": {"event": "ditsh:stop_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": true}]}}, "minecraft:ambient_sound_interval": {"event_name": "ambient", "range": 4, "value": 3}}}}