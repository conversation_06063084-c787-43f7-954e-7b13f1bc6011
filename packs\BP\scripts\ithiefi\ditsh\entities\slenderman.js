import { EntityDamageCause, GameMode } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const TELEPORT_OFFSET_DISTANCE = 5;
const LOOK_DAMAGE_AMOUNT = 1;
const NAUSEA_DURATION = 100;
const RAYCAST_LENGTH = 128;
const RAYCAST_STEP = 2;
const RAYCAST_RADIUS = 5;
export function slendermanOnPlayerStartLooking(slenderman) {
    try {
        const slendermanLocation = slenderman.location;
        const players = slenderman.dimension.getPlayers({
            location: slendermanLocation,
            maxDistance: 128,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        for (const player of players) {
            if (isPlayerLookingAtSlenderman(player, slendermanLocation)) {
                player.addEffect("nausea", NAUSEA_DURATION, {
                    amplifier: 0,
                    showParticles: false
                });
                player.applyDamage(LOOK_DAMAGE_AMOUNT, {
                    cause: EntityDamageCause.entityAttack,
                    damagingEntity: slenderman
                });
            }
        }
    }
    catch (error) {
        console.warn(`Failed to apply Slenderman look effects: ${error}`);
    }
}
function isPlayerLookingAtSlenderman(player, slendermanLocation) {
    try {
        const playerLocation = player.getHeadLocation();
        const viewDirection = player.getViewDirection();
        const maxDistance = RAYCAST_LENGTH;
        const raycastStep = RAYCAST_STEP;
        const detectionRadius = RAYCAST_RADIUS;
        const raycastPoints = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);
        for (const rayPoint of raycastPoints) {
            const distanceToSlenderman = getDistance(rayPoint, slendermanLocation);
            if (distanceToSlenderman <= detectionRadius) {
                if (rayPoint.y >= slendermanLocation.y && rayPoint.y <= slendermanLocation.y + 3.0) {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        console.warn(`Failed to check if player is looking at Slenderman: ${error}`);
        return false;
    }
}
export function slendermanTeleportHandler(slenderman) {
    try {
        const location = slenderman.location;
        const players = slenderman.dimension.getPlayers({
            location: location,
            maxDistance: 256,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        if (players.length === 0) {
            return;
        }
        let nearestPlayer = players[0];
        let nearestDistance = getDistance(location, nearestPlayer.location);
        for (const player of players) {
            const distance = getDistance(location, player.location);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }
        let finalTeleportLocation;
        const playerViewDirection = nearestPlayer.getViewDirection();
        const inFrontOfPlayerLocation = {
            x: nearestPlayer.location.x + (playerViewDirection.x * 3),
            y: nearestPlayer.location.y,
            z: nearestPlayer.location.z + (playerViewDirection.z * 3)
        };
        const inFrontPlayerBlock = slenderman.dimension.getBlock(inFrontOfPlayerLocation);
        if (inFrontPlayerBlock?.isAir) {
            finalTeleportLocation = inFrontOfPlayerLocation;
        }
        if (!finalTeleportLocation) {
            const behindPlayerLocation = {
                x: nearestPlayer.location.x - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE),
                y: nearestPlayer.location.y,
                z: nearestPlayer.location.z - (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE)
            };
            const behindPlayerBlock = slenderman.dimension.getBlock(behindPlayerLocation);
            if (behindPlayerBlock?.isAir) {
                finalTeleportLocation = behindPlayerLocation;
            }
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = getRandomLocation(nearestPlayer.location, slenderman.dimension, 3, 5, 0, true);
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = getRandomLocation(nearestPlayer.location, slenderman.dimension, 2, 2, 0, true);
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = {
                x: nearestPlayer.location.x,
                y: nearestPlayer.location.y,
                z: nearestPlayer.location.z
            };
        }
        slenderman.teleport(finalTeleportLocation);
        slenderman.dimension.playSound("mob.ditsh.slenderman.spot", finalTeleportLocation);
    }
    catch (error) {
        console.warn(`Failed to teleport Slenderman: ${error}`);
    }
}
