import { system } from "@minecraft/server";
export async function scp096OnKillHandler(entity) {
    try {
        if (!entity || !entity.isValid || entity.typeId !== "ditsh:scp096") {
            return;
        }
        await system.waitTicks(40);
        if (!entity || !entity.isValid) {
            return;
        }
        entity.triggerEvent("ditsh:start_sitting");
    }
    catch (error) {
    }
}
