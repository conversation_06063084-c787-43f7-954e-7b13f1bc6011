{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ditsh:murder_monkey", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ithiefi/ditsh/entities/murder monkey"}, "geometry": {"default": "geometry.ithiefi_ditsh_murder_monkey"}, "animations": {"look_at_target": "animation.common.look_at_target", "idle": "animation.ithiefi_ditsh_murder_monkey.idle", "walk": "animation.ithiefi_ditsh_murder_monkey.walk", "attack": "animation.ithiefi_ditsh_murder_monkey.attack", "general": "controller.animation.ithiefi_ditsh_murder_monkey.general"}, "scripts": {"animate": [{"look_at_target": "q.is_alive"}, "general"]}, "render_controllers": ["controller.render.default"], "spawn_egg": {"base_color": "#5e381b", "overlay_color": "#cf6161"}}}}