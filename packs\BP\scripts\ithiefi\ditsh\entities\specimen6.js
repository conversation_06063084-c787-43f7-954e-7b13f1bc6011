import { GameMode, system } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const TELEPORT_OFFSET_DISTANCE = 3;
const IDLE_TIMEOUT_TICKS = 8 * 20;
const LOOK_DETECTION_RADIUS = 128;
const TELEPORT_COOLDOWN_TICKS = 3 * 20;
const RAYCAST_LENGTH = 128;
const RAYCAST_STEP = 2;
const RAYCAST_RADIUS = 5;
const playerIdleTracker = new Map();
const specimen6CooldownTracker = new Map();
function isPlayerLookingAtSpecimen6(player, specimen6Location) {
    try {
        const playerLocation = player.location;
        const viewDirection = player.getViewDirection();
        const raycastResults = fixedLenRaycast(playerLocation, viewDirection, RAYCAST_LENGTH, RAYCAST_STEP);
        for (const raycastPoint of raycastResults) {
            const distanceToSpecimen6 = getDistance(raycastPoint, specimen6Location);
            if (distanceToSpecimen6 <= RAYCAST_RADIUS) {
                return true;
            }
        }
        return false;
    }
    catch (error) {
        console.warn(`Failed to check if player is looking at Specimen 6: ${error}`);
        return false;
    }
}
function updatePlayerIdleTracking(player) {
    const playerId = player.id;
    const currentTick = system.currentTick;
    const currentPosition = player.location;
    const existingTracker = playerIdleTracker.get(playerId);
    if (!existingTracker) {
        playerIdleTracker.set(playerId, {
            lastPosition: currentPosition,
            lastMovementTick: currentTick,
            isIdle: false
        });
        return;
    }
    const distanceMoved = getDistance(existingTracker.lastPosition, currentPosition);
    if (distanceMoved > 0.5) {
        existingTracker.lastPosition = currentPosition;
        existingTracker.lastMovementTick = currentTick;
        existingTracker.isIdle = false;
    }
    else {
        const ticksSinceLastMovement = currentTick - existingTracker.lastMovementTick;
        existingTracker.isIdle = ticksSinceLastMovement >= IDLE_TIMEOUT_TICKS;
    }
}
function getPlayersNotLookingAtSpecimen6(specimen6) {
    const specimen6Location = specimen6.location;
    const playersNotLooking = [];
    const players = specimen6.dimension.getPlayers({
        location: specimen6Location,
        maxDistance: LOOK_DETECTION_RADIUS,
        excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });
    for (const player of players) {
        updatePlayerIdleTracking(player);
        if (!isPlayerLookingAtSpecimen6(player, specimen6Location)) {
            playersNotLooking.push(player);
        }
    }
    return playersNotLooking;
}
function getIdlePlayers(specimen6) {
    const specimen6Location = specimen6.location;
    const idlePlayers = [];
    const players = specimen6.dimension.getPlayers({
        location: specimen6Location,
        maxDistance: 256,
        excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });
    for (const player of players) {
        updatePlayerIdleTracking(player);
        const tracker = playerIdleTracker.get(player.id);
        if (tracker?.isIdle) {
            idlePlayers.push(player);
        }
    }
    return idlePlayers;
}
export function specimen6TeleportBehindPlayer(specimen6) {
    try {
        const specimen6Id = specimen6.id;
        const currentTick = system.currentTick;
        const lastTeleportTick = specimen6CooldownTracker.get(specimen6Id) || 0;
        if (currentTick - lastTeleportTick < TELEPORT_COOLDOWN_TICKS) {
            return;
        }
        const playersNotLooking = getPlayersNotLookingAtSpecimen6(specimen6);
        if (playersNotLooking.length === 0) {
            return;
        }
        const targetPlayer = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)];
        const playerViewDirection = targetPlayer.getViewDirection();
        let finalTeleportLocation;
        const behindPlayerLocation = {
            x: targetPlayer.location.x - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE),
            y: targetPlayer.location.y,
            z: targetPlayer.location.z - (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE)
        };
        const behindPlayerBlock = specimen6.dimension.getBlock(behindPlayerLocation);
        if (behindPlayerBlock?.isAir) {
            finalTeleportLocation = behindPlayerLocation;
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = getRandomLocation(targetPlayer.location, specimen6.dimension, 2, 4, 0, true);
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = {
                x: targetPlayer.location.x + (Math.random() - 0.5) * 4,
                y: targetPlayer.location.y,
                z: targetPlayer.location.z + (Math.random() - 0.5) * 4
            };
        }
        specimen6.teleport(finalTeleportLocation);
        specimen6.dimension.playSound("mob.ditsh.specimen6.step", finalTeleportLocation);
        specimen6CooldownTracker.set(specimen6Id, currentTick);
    }
    catch (error) {
        console.warn(`Failed to teleport Specimen 6 behind player: ${error}`);
    }
}
export function specimen6TeleportToIdlePlayer(specimen6) {
    try {
        const specimen6Id = specimen6.id;
        const currentTick = system.currentTick;
        const lastTeleportTick = specimen6CooldownTracker.get(specimen6Id) || 0;
        if (currentTick - lastTeleportTick < TELEPORT_COOLDOWN_TICKS) {
            return;
        }
        const idlePlayers = getIdlePlayers(specimen6);
        if (idlePlayers.length === 0) {
            return;
        }
        let nearestIdlePlayer = idlePlayers[0];
        let nearestDistance = getDistance(specimen6.location, nearestIdlePlayer.location);
        for (const player of idlePlayers) {
            const distance = getDistance(specimen6.location, player.location);
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestIdlePlayer = player;
            }
        }
        let finalTeleportLocation;
        finalTeleportLocation = getRandomLocation(nearestIdlePlayer.location, specimen6.dimension, 3, 5, 0, true);
        if (!finalTeleportLocation) {
            finalTeleportLocation = {
                x: nearestIdlePlayer.location.x,
                y: nearestIdlePlayer.location.y,
                z: nearestIdlePlayer.location.z
            };
        }
        specimen6.teleport(finalTeleportLocation);
        specimen6.dimension.playSound("mob.ditsh.specimen6.step", finalTeleportLocation);
        specimen6CooldownTracker.set(specimen6Id, currentTick);
    }
    catch (error) {
        console.warn(`Failed to teleport Specimen 6 to idle player: ${error}`);
    }
}
export function cleanupSpecimen6PlayerTracking() {
    const currentTick = system.currentTick;
    const expiredEntries = [];
    for (const [playerId, tracker] of playerIdleTracker.entries()) {
        if (currentTick - tracker.lastMovementTick > 6000) {
            expiredEntries.push(playerId);
        }
    }
    for (const playerId of expiredEntries) {
        playerIdleTracker.delete(playerId);
    }
    const expiredCooldowns = [];
    for (const [specimen6Id, lastTeleportTick] of specimen6CooldownTracker.entries()) {
        if (currentTick - lastTeleportTick > TELEPORT_COOLDOWN_TICKS * 2) {
            expiredCooldowns.push(specimen6Id);
        }
    }
    for (const specimen6Id of expiredCooldowns) {
        specimen6CooldownTracker.delete(specimen6Id);
    }
}
