import { GameMode, system, world } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const TELEPORT_OFFSET_DISTANCE = 3;
const IDLE_TIMEOUT_TICKS = 8 * 20;
const LOOK_DETECTION_RADIUS = 128;
const TELEPORT_COOLDOWN_TICKS = 3 * 20;
const RAYCAST_LENGTH = 128;
const RAYCAST_STEP = 2;
const RAYCAST_RADIUS = 5;
const playerPositionTracker = new Map();
const specimen6CooldownTracker = new Map();
const activeSpecimen6Entities = new Set();
function isPlayerLookingAtSpecimen6(player, specimen6Location) {
    try {
        const playerLocation = player.location;
        const viewDirection = player.getViewDirection();
        const raycastResults = fixedLenRaycast(playerLocation, viewDirection, RAYCAST_LENGTH, RAYCAST_STEP);
        for (const raycastPoint of raycastResults) {
            const distanceToSpecimen6 = getDistance(raycastPoint, specimen6Location);
            if (distanceToSpecimen6 <= RAYCAST_RADIUS) {
                return true;
            }
        }
        return false;
    }
    catch (error) {
        console.warn(`Failed to check if player is looking at Specimen 6: ${error}`);
        return false;
    }
}
function initializeIdlePlayerDetection() {
    system.runInterval(() => {
        checkForIdlePlayers();
    }, IDLE_TIMEOUT_TICKS);
}
function checkForIdlePlayers() {
    try {
        const allPlayers = [];
        for (const player of world.getAllPlayers()) {
            try {
                allPlayers.push(player);
            }
            catch (error) {
                continue;
            }
        }
        for (const player of allPlayers) {
            const playerId = player.id;
            const currentPosition = player.location;
            const storedPosition = playerPositionTracker.get(playerId);
            if (storedPosition) {
                const distanceMoved = getDistance(storedPosition, currentPosition);
                if (distanceMoved <= 0.5) {
                    const nearbySpecimen6Entities = player.dimension.getEntities({
                        type: "ditsh:specimen6",
                        location: currentPosition,
                        maxDistance: 256
                    });
                    for (const specimen6 of nearbySpecimen6Entities) {
                        const specimen6Id = specimen6.id;
                        const currentTick = system.currentTick;
                        const lastTeleportTick = specimen6CooldownTracker.get(specimen6Id) || 0;
                        if (currentTick - lastTeleportTick >= TELEPORT_COOLDOWN_TICKS) {
                            specimen6.teleport(currentPosition);
                            specimen6.dimension.playSound("mob.ditsh.specimen6.step", currentPosition);
                            specimen6CooldownTracker.set(specimen6Id, currentTick);
                            specimen6.triggerEvent("ditsh:teleport_to_idle_player");
                        }
                    }
                }
            }
            playerPositionTracker.set(playerId, currentPosition);
        }
    }
    catch (error) {
        console.warn(`Failed to check for idle players: ${error}`);
    }
}
function getPlayersNotLookingAtSpecimen6(specimen6) {
    const specimen6Location = specimen6.location;
    const playersNotLooking = [];
    const players = specimen6.dimension.getPlayers({
        location: specimen6Location,
        maxDistance: LOOK_DETECTION_RADIUS,
        excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });
    for (const player of players) {
        if (!isPlayerLookingAtSpecimen6(player, specimen6Location)) {
            playersNotLooking.push(player);
        }
    }
    return playersNotLooking;
}
export function specimen6TeleportBehindPlayer(specimen6) {
    try {
        const specimen6Id = specimen6.id;
        const currentTick = system.currentTick;
        const lastTeleportTick = specimen6CooldownTracker.get(specimen6Id) || 0;
        if (currentTick - lastTeleportTick < TELEPORT_COOLDOWN_TICKS) {
            return;
        }
        const playersNotLooking = getPlayersNotLookingAtSpecimen6(specimen6);
        if (playersNotLooking.length === 0) {
            return;
        }
        const targetPlayer = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)];
        const playerViewDirection = targetPlayer.getViewDirection();
        let finalTeleportLocation;
        const behindPlayerLocation = {
            x: targetPlayer.location.x - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE),
            y: targetPlayer.location.y,
            z: targetPlayer.location.z - (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE)
        };
        const behindPlayerBlock = specimen6.dimension.getBlock(behindPlayerLocation);
        if (behindPlayerBlock?.isAir) {
            finalTeleportLocation = behindPlayerLocation;
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = getRandomLocation(targetPlayer.location, specimen6.dimension, 2, 4, 0, true);
        }
        if (!finalTeleportLocation) {
            finalTeleportLocation = {
                x: targetPlayer.location.x + (Math.random() - 0.5) * 4,
                y: targetPlayer.location.y,
                z: targetPlayer.location.z + (Math.random() - 0.5) * 4
            };
        }
        specimen6.teleport(finalTeleportLocation);
        specimen6.dimension.playSound("mob.ditsh.specimen6.step", finalTeleportLocation);
        specimen6CooldownTracker.set(specimen6Id, currentTick);
    }
    catch (error) {
        console.warn(`Failed to teleport Specimen 6 behind player: ${error}`);
    }
}
export function specimen6TeleportToIdlePlayer(specimen6) {
    try {
        activeSpecimen6Entities.add(specimen6.id);
    }
    catch (error) {
        console.warn(`Failed to register Specimen 6 for idle player detection: ${error}`);
    }
}
export function cleanupSpecimen6PlayerTracking() {
    const currentTick = system.currentTick;
    const expiredPositions = [];
    for (const playerId of playerPositionTracker.keys()) {
        try {
            const player = world.getAllPlayers().find(p => p.id === playerId);
            if (!player) {
                expiredPositions.push(playerId);
            }
        }
        catch (error) {
            expiredPositions.push(playerId);
        }
    }
    for (const playerId of expiredPositions) {
        playerPositionTracker.delete(playerId);
    }
    const expiredCooldowns = [];
    for (const [specimen6Id, lastTeleportTick] of specimen6CooldownTracker.entries()) {
        if (currentTick - lastTeleportTick > TELEPORT_COOLDOWN_TICKS * 2) {
            expiredCooldowns.push(specimen6Id);
        }
    }
    for (const specimen6Id of expiredCooldowns) {
        specimen6CooldownTracker.delete(specimen6Id);
    }
    const expiredEntities = [];
    for (const specimen6Id of activeSpecimen6Entities) {
        try {
            let entityExists = false;
            for (const dimension of [world.getDimension("overworld"), world.getDimension("nether"), world.getDimension("the_end")]) {
                const entities = dimension.getEntities({ type: "ditsh:specimen6" });
                if (entities.some(e => e.id === specimen6Id)) {
                    entityExists = true;
                    break;
                }
            }
            if (!entityExists) {
                expiredEntities.push(specimen6Id);
            }
        }
        catch (error) {
            expiredEntities.push(specimen6Id);
        }
    }
    for (const specimen6Id of expiredEntities) {
        activeSpecimen6Entities.delete(specimen6Id);
    }
}
export function initializeSpecimen6Systems() {
    initializeIdlePlayerDetection();
}
