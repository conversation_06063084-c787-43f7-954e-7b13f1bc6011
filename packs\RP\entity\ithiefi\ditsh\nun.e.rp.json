{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ditsh:nun", "min_engine_version": "1.16.0", "materials": {"default": "entity_emissive_alpha"}, "textures": {"default": "textures/ithiefi/ditsh/entities/nun/nun"}, "geometry": {"default": "geometry.ithiefi_ditsh_nun"}, "animations": {"look_at_target": "animation.common.look_at_target", "idle": "animation.ithiefi_ditsh_nun.idle", "walk": "animation.ithiefi_ditsh_nun.walk", "attack": "animation.ithiefi_ditsh_nun.attack", "general": "controller.animation.ithiefi_ditsh_nun.general"}, "scripts": {"animate": [{"look_at_target": "q.is_alive"}, "general"]}, "render_controllers": ["controller.render.default"], "spawn_egg": {"base_color": "#343030", "overlay_color": "#ab3535"}}}}