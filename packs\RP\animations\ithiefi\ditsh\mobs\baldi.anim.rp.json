{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_baldi.attack": {"loop": "hold_on_last_frame", "animation_length": 0.4583, "bones": {"rightArm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0417": [-77.38, 12.09, 1.83], "0.125": [-164.17, 7.21, -15.72], "0.1667": [-211.01, -13.22, -33.19], "0.25": [-48.19, 2.32, -61.96], "0.2917": [-38.19, 2.32, -61.96], "0.375": [-42.19, 17.39, 18.25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -3], "0.2917": [0, 0, -1.5], "0.375": [0, 0, 0]}}, "leftArm": {"rotation": {"0.0": [-5, 0, -25], "0.0417": [-52.5, 0, -25], "0.125": [-75.12, 7.09, -12.78], "0.1667": [-69.09, 23.14, 3.28], "0.2083": [-105.9, -35.16, 18.23], "0.2917": [-93.37, -44.72, 22.07], "0.3333": [-81.64, -54.7, 23.08], "0.375": [-39.62, -27.97, -0.9], "0.4583": [-5, 0, -25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.2083": [0, -1, 3], "0.2917": [0, -0.75, 2.25], "0.3333": [0, -0.5, 1.5], "0.375": [0, -0.25, 0.75], "0.4583": [0, 0, 0]}}, "head": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [2.5, 0, 0], "0.125": [10.01, -5, -0.22], "0.1667": [10.01, -5, -0.22], "0.25": [-13.1, 7.73, -0.57], "0.2917": [-13.18, 25.33, -2.19], "0.375": [4.11, 27.32, 4.11], "0.4167": [2.83, 10.15, -2.21], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [7.5, 0, 0], "0.125": [2.53, 4.96, 0.65], "0.1667": [0.03, 4.96, 0.65], "0.25": [21.01, -8.45, 0.48], "0.2917": [28.92, -26.24, -9.15], "0.375": [13.92, -26.24, -9.15], "0.4167": [6.79, -10.25, 0.01], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}}}, "animation.ithiefi_ditsh_baldi.move": {"loop": true, "animation_length": 1.2083, "bones": {"rightArm": {"rotation": [-95.89834, 64.88331, -95.34402], "position": [-1, 0, 0]}, "joint": {"rotation": [-180, "math.clamp(-3.7821+math.sin(600*q.anim_time)*64, -64, 0)", 115], "position": [0, 0, 1]}, "leftArm": {"rotation": [-81.03128, -66.53988, 79.51521], "position": [1, 0, 0]}, "joint2": {"rotation": [0, 0, 62.5]}, "leftLeg": {"rotation": ["math.cos(900*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time* 900) * 2,0 , 8)", 0]}, "rightLeg": {"rotation": ["-math.cos(900*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time* 900) * 2,0 , 8)", 0]}, "head": {"position": [0, "math.cos(600*q.anim_time-40)*0.2", 0]}, "torso": {"position": ["math.cos(300*q.anim_time)*0.3", "-1+math.sin(600*q.anim_time)*0.5", 0]}}, "sound_effects": {"0.5833": {"effect": "move"}, "1.1667": {"effect": "move"}}}}}