{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_armless.idle": {"loop": true, "animation_length": 3.5834, "bones": {"root": {"rotation": ["math.sin(100*q.anim_time)*4", 0, "math.cos(100*q.anim_time)*4"]}, "torso": {"rotation": ["math.sin(100*q.anim_time-80)*6", 0, "math.cos(100*q.anim_time-80)*6"]}, "head": {"rotation": ["math.sin(100*q.anim_time-160)*8", 0, "math.cos(100*q.anim_time-160)*8"]}}}, "animation.ithiefi_ditsh_armless.walk": {"loop": true, "animation_length": 3.5834, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 2))", "bones": {"root": {"position": [0, "-0.45+math.sin(800*q.anim_time-80) * 0.5", 0]}, "leftLeg": {"rotation": ["math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time* 400) * 2,0 , 8)", 0]}, "rightLeg": {"rotation": ["-math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time* 400) * 2,0 , 8)", 0]}, "torso": {"rotation": ["math.cos(q.anim_time * 800) * 1", "math.cos(100*q.anim_time) * 8", "math.sin(100*q.anim_time) * 8"], "position": [0, "math.sin(q.anim_time * 800) * 0.3", 0]}, "head": {"rotation": ["math.sin(800*q.anim_time-20) * 4", "math.cos(100*q.anim_time-80) * 8", "math.sin(100*q.anim_time-80) * 12"]}}}, "animation.ithiefi_ditsh_armless.attack": {"loop": "hold_on_last_frame", "animation_length": 0.448, "bones": {"torso": {"rotation": ["math.clamp(math.sin(800*q.anim_time)*64, 0, 64)", 0, 0]}, "head": {"rotation": ["math.clamp(math.sin(800*q.anim_time-50)*64, 0, 64)", 0, 0]}}}}}