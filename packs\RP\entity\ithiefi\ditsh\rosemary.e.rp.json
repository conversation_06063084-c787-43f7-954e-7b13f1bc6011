{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ditsh:rosemary", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ithiefi/ditsh/entities/rosemary"}, "geometry": {"default": "geometry.ithiefi_ditsh_rosemary"}, "animations": {"look_at_target": "animation.common.look_at_target", "idle": "animation.ithiefi_ditsh_rosemary.idle", "walk": "animation.ithiefi_ditsh_rosemary.walk", "run": "animation.ithiefi_ditsh_rosemary.run", "attack": "animation.ithiefi_ditsh_rosemary.attack", "general": "controller.animation.ithiefi_ditsh_rosemary.general"}, "scripts": {"animate": [{"look_at_target": "q.is_alive"}, "general"]}, "render_controllers": ["controller.render.default"], "spawn_egg": {"base_color": "#735e31", "overlay_color": "#793636"}}}}