{"$schema": "https://raw.githubusercontent.com/Bedrock-OSS/regolith-schemas/main/config/v1.4.json", "author": "Raboy13", "name": "DitSH Add-On", "packs": {"behaviorPack": "./packs/BP", "resourcePack": "./packs/RP"}, "regolith": {"dataPath": "./packs/data", "filterDefinitions": {"texture_list": {"url": "github.com/Bedrock-OSS/regolith-filters", "version": "1.1.3"}}, "formatVersion": "1.4.0", "profiles": {"build": {"export": {"readOnly": false, "target": "local"}, "filters": [{"filter": "texture_list"}]}, "default": {"export": {"build": "standard", "readOnly": false, "target": "development"}, "filters": []}}}}