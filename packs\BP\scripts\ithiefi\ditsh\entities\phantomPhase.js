import { system } from "@minecraft/server";
import { getRandomLocation, getDistance } from "../../../utilities/vector3";
import { teleport } from "../../../utilities/teleport";
import { getTarget } from "../../general_mechanics/targetUtils";
const TELEPORT_TIMING = 37;
const TOTAL_ANIMATION_TIME = 83;
const COOLDOWN_TIME = 20;
const PHANTOM_PHASE_CONFIG = {
    MIN_DISTANCE: 12,
    MAX_DISTANCE: 24,
    MIN_DISTANCE_FROM_SELF: 16,
    MAX_ATTEMPTS: 64
};
export function executePhantomPhaseAbility(necromancer) {
    let teleportTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(teleportTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "phantom_phase_start") {
                performPhantomPhaseTeleport(necromancer);
            }
        }
        catch (error) {
            system.clearRun(teleportTiming);
            necromancer.triggerEvent("ptd_dbb:phantom_phase_end_attack");
        }
    }, TELEPORT_TIMING);
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "phantom_phase_end") {
                necromancer.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, TOTAL_ANIMATION_TIME);
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            necromancer.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, TOTAL_ANIMATION_TIME + COOLDOWN_TIME);
}
async function performPhantomPhaseTeleport(necromancer) {
    try {
        const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
        if (!target) {
            necromancer.triggerEvent("ptd_dbb:phantom_phase_end_attack");
            return;
        }
        const necromancerLocation = { ...necromancer.location };
        const targetLocation = target.location;
        let validLocation;
        let attempts = 0;
        while (!validLocation && attempts < PHANTOM_PHASE_CONFIG.MAX_ATTEMPTS) {
            attempts++;
            const potentialLocation = getRandomLocation(targetLocation, necromancer.dimension, PHANTOM_PHASE_CONFIG.MIN_DISTANCE, PHANTOM_PHASE_CONFIG.MAX_DISTANCE - PHANTOM_PHASE_CONFIG.MIN_DISTANCE, 0, true);
            if (!potentialLocation)
                continue;
            const distanceFromSelf = getDistance(necromancerLocation, potentialLocation);
            if (distanceFromSelf >= PHANTOM_PHASE_CONFIG.MIN_DISTANCE_FROM_SELF) {
                validLocation = potentialLocation;
            }
        }
        if (!validLocation) {
            necromancer.triggerEvent("ptd_dbb:phantom_phase_end_attack");
            return;
        }
        const adjustedLocation = findNearestAirBlockAtY(necromancer.dimension, validLocation, targetLocation.y);
        if (!adjustedLocation)
            return;
        await teleport(necromancer, adjustedLocation);
        await system.waitTicks(6);
        necromancer.triggerEvent("ptd_dbb:phantom_phase_end_attack");
    }
    catch (error) {
        console.warn(`Error in phantom phase ability: ${error}`);
        necromancer.triggerEvent("ptd_dbb:phantom_phase_end_attack");
    }
}
function findNearestAirBlockAtY(dimension, location, targetY) {
    const adjustedLocation = { ...location };
    adjustedLocation.y = targetY;
    if (isAirBlock(dimension, adjustedLocation)) {
        return adjustedLocation;
    }
    const MAX_SEARCH_DISTANCE = 10;
    for (let offset = 1; offset <= MAX_SEARCH_DISTANCE; offset++) {
        adjustedLocation.y = targetY + offset;
        if (isAirBlock(dimension, adjustedLocation)) {
            return adjustedLocation;
        }
        adjustedLocation.y = targetY - offset;
        if (isAirBlock(dimension, adjustedLocation)) {
            return adjustedLocation;
        }
    }
    return undefined;
}
function isAirBlock(dimension, location) {
    const block = dimension.getBlock(location);
    return block?.isAir ?? false;
}
