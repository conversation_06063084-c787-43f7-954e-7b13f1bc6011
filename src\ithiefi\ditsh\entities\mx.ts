import { Entity, system, Vector3, Player, GameMode } from "@minecraft/server";
import { getDistance, getDirection } from "../utilities/vector3";

/**
 * Checks if the MX entity is about to hit a wall and plays a sound if so.
 * and stops the dash attack.
 *
 * @param {Entity} mx - The MX entity to check.
 */
export function mxCheckForWallHit(mx: Entity): void {
  try {
    const isDashing = mx.getProperty("ditsh:dashing") as boolean;
    if (!isDashing) return;

    const location: Vector3 = { x: mx.location.x, y: mx.location.y + 2, z: mx.location.z };
    const direction = mx.getViewDirection();
    // 3 blocks offset in front of mx
    const offset: Vector3 = { x: location.x + direction.x * 3, y: location.y, z: location.z + direction.z * 3 };
    const block = mx.dimension.getBlock(offset);
    if (block && !block.isAir) {
      mx.triggerEvent("ditsh:on_dash_attack");
      mx.dimension.playSound("mob.ditsh.mx.hit_wall", location);
    }
  } catch (e) {} // Handle errors silently
  return;
}

/**
 * Executes MX's jump attack - warns players, calculates impulse based on distance to nearest player,
 * and applies slowness V to players who don't jump in time.
 *
 * @param {Entity} mx - The MX entity performing the jump attack.
 */
export async function mxJump(mx: Entity): Promise<void> {
  try {
    // Set jumping property to true
    mx.setProperty("ditsh:jumping", true);
    
    // Find the nearest player
    const nearbyPlayers: Player[] = mx.dimension.getPlayers({
      type: "minecraft:player",
      location: mx.location,
      maxDistance: 64,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    if (nearbyPlayers.length === 0) {
      // No players nearby, reset jumping property and return
      mx.setProperty("ditsh:jumping", false);
      return;
    }

    // Find the closest player
    let nearestPlayer: Player = nearbyPlayers[0]!;
    let shortestDistance: number = getDistance(mx.location, nearestPlayer.location);

    for (const player of nearbyPlayers) {
      const distance: number = getDistance(mx.location, player.location);
      if (distance < shortestDistance) {
        shortestDistance = distance;
        nearestPlayer = player;
      }
    }

    // Warn all nearby players about the incoming jump attack
    for (const player of nearbyPlayers) {
      player.onScreenDisplay.setActionBar("§c§lMX is about to JUMP!§r");
    }

    // Wait 10 ticks (0.5 seconds) to give players time to react
    await system.waitTicks(20);

    // Calculate impulse power based on distance to nearest player
    // Closer players = less power (minimum 0.5), farther players = more power (maximum 2.0)
    const minPower: number = 0.5;
    const maxPower: number = 8.0;
    const maxDistance: number = 64; // Maximum effective distance for power calculation

    const normalizedDistance: number = Math.min(shortestDistance, maxDistance) / maxDistance;
    const impulsePower: number = minPower + (normalizedDistance * (maxPower - minPower));

    // Get direction to nearest player for arch-like movement
    const direction: Vector3 = getDirection(mx.location, nearestPlayer.location);

    // Create impulse vector with arch-like trajectory (higher Y component for jumping arc)
    const impulseVector: Vector3 = {
      x: direction.x * impulsePower,
      y: Math.max(0.8, impulsePower * 0.6), // Ensure minimum upward force for arc
      z: direction.z * impulsePower
    };

    // Apply slowness V to all nearby players at the same time as MX jumps
    for (const player of nearbyPlayers) {
      try {
        // Apply slowness V for 10 seconds (200 ticks) to all players
        player.addEffect("slowness", 200, {
          amplifier: 4, // Slowness V (amplifier 4 = level 5)
          showParticles: false
        });
      } catch (e) {
        // Handle individual player errors silently
      }
    }

    // Apply the impulse to MX
    mx.applyImpulse(impulseVector);

    // Play jump sound
    mx.dimension.playSound("mob.ditsh.mx.jump", mx.location);

    // Wait 10 ticks after jump, then enable ground detection
    await system.waitTicks(10);
    mx.setProperty("ditsh:ground_check", true);

  } catch (e) {
    // Handle errors silently and ensure properties are reset
    try {
      mx.setProperty("ditsh:jumping", false);
      mx.setProperty("ditsh:ground_check", false);
    } catch (resetError) {}
  }
  return;
}
