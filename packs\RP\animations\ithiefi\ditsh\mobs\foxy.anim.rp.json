{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_foxy.twitch": {"loop": true, "animation_length": 5}, "animation.ithiefi_ditsh_foxy.idle": {"loop": true, "animation_length": 5, "bones": {"leftArm": {"rotation": {"0.0": [0, 0, 0], "0.0417": [0, 0, -17.64806], "0.0833": [0, 0, -22.75595], "0.125": [0, 0, -25.65138], "0.1667": [0, 0, -27.5], "0.2083": [0, 0, -28.27292], "0.25": [0, 0, -28.68995], "0.7917": [0, 0, -30.62566], "1.3333": [0, 0, -31.01264], "1.875": [0, 0, -30.5557], "2.25": [0, 0, -29.46199], "2.375": [0, 0, -28.68995], "2.4167": [0, 0, -28.27292], "2.4583": [0, 0, -27.5], "2.5": [0, 0, -25.65138], "2.5417": [0, 0, -22.75595], "2.5833": [0, 0, -17.64806], "2.625": [0, 0, 0]}}, "left_arm_knee": {"rotation": {"0.0": [0, 0, 0], "0.0417": [0, 0, -17.64806], "0.0833": [0, 0, -22.75595], "0.125": [0, 0, -25.65138], "0.1667": [0, 0, -27.5], "0.2083": [-4.66317, 0, -28.8738], "0.25": [-28.75, 0, -29.56435], "0.2917": [-52.83683, 0, -27.91093], "0.3333": [-57.5, 0, -27.5], "0.875": [-57.5, 0, -27.5], "1.4167": [-57.5, 0, -27.5], "1.9583": [-57.5, 0, -27.5], "2.1667": [-57.5, 0, -27.5], "2.2083": [-52.83683, 0, -27.91093], "2.25": [-28.75, 0, -29.56435], "2.2917": [-4.66317, 0, -28.8738], "2.3333": [0, 0, -27.5], "2.375": [0, 0, -25.65138], "2.4167": [0, 0, -22.75595], "2.4583": [0, 0, -17.64806], "2.5": [0, 0, 0]}}, "rightArm": {"rotation": {"1.2917": [0, 0, 0], "1.3333": [0, 0, 17.64806], "1.375": [0, 0, 22.75595], "1.4167": [0, 0, 25.65138], "1.4583": [0, 0, 27.5], "1.5": [-4.66317, 0, 28.8738], "1.5417": [-28.75, 0, 29.56435], "1.5833": [-52.83683, 0, 27.91093], "1.625": [-57.5, 0, 27.5], "2.1667": [-57.5, 0, 27.5], "2.7083": [-57.5, 0, 27.5], "3.125": [-57.5, 0, 27.5], "3.1667": [-52.83683, 0, 27.91093], "3.2083": [-28.75, 0, 29.56435], "3.25": [-4.66317, 0, 28.8738], "3.2917": [0, 0, 27.5], "3.3333": [0, 0, 25.65138], "3.375": [0, 0, 22.75595], "3.4167": [0, 0, 17.64806], "3.4583": [0, 0, 0]}}, "right_arm_knee": {"rotation": {"1.2917": [0, 0, 0], "1.3333": [0, 0, 17.64806], "1.375": [0, 0, 22.75595], "1.4167": [0, 0, 25.65138], "1.4583": [0, 0, 27.5], "1.5": [-4.66317, 0, 28.8738], "1.5417": [-28.75, 0, 29.56435], "1.5833": [-52.83683, 0, 27.91093], "1.625": [-57.5, 0, 27.5], "2.1667": [-57.5, 0, 27.5], "2.7083": [-57.5, 0, 27.5], "2.9583": [-57.5, 0, 27.5], "3.0": [-52.83683, 0, 27.91093], "3.0417": [-28.75, 0, 29.56435], "3.0833": [-4.66317, 0, 28.8738], "3.125": [0, 0, 27.5], "3.1667": [0, 0, 25.65138], "3.2083": [0, 0, 22.75595], "3.25": [0, 0, 17.64806], "3.2917": [0, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-30, 42.5, 0], "1.0": [-30, 42.5, 0], "1.0833": [-30, -42.5, 0], "2.0": [-30, -42.5, 0], "2.25": [0, -355, 0]}}, "left_eyelid": {"position": {"0.0": [0, 0, 0], "0.0833": [0, -1, 0], "0.3333": [0, -1, 0], "0.5": [0, 0, 0], "2.5833": [0, 0, 0], "2.6667": [0, -1, 0], "2.9167": [0, -1, 0], "3.0833": [0, 0, 0]}}, "torso": {"rotation": {"0.75": [0, 0, 0], "0.7917": [-0.09023, 0.01188, 3.16426], "0.8333": [-0.2701, 0.03555, 4.21586], "0.875": [-0.48936, 0.06441, 4.91984], "0.9167": [-0.72985, 0.09606, 5.44477], "1.2083": [-2.3884, 0.31434, 7.24156], "1.25": [-2.47864, 0.32621, 7.49294], "1.2917": [-2.44478, 0.32176, 7.40406], "1.8333": [-1.7147, 0.22567, 3.40377], "2.375": [-0.94171, 0.12394, 0.08962], "2.75": [-0.41315, 0.05438, -1.03313], "2.9583": [-0.13374, 0.0176, -0.78034], "3.0833": [0, 0, 0]}}}}, "animation.ithiefi_ditsh_foxy.walk": {"loop": true, "animation_length": 0.9167, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 2))", "bones": {"root": {"position": [0, "math.clamp(-math.sin(800*q.anim_time+85)*1,-0.4,24)", 0]}, "leftLeg": {"rotation": ["math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time*400) * 2,0 , 8)", 0]}, "left_leg_knee": {"rotation": ["math.clamp(math.cos(400*q.anim_time-40) * 24, 0, 64)", 0, 0]}, "left_foot": {"rotation": ["math.clamp(math.cos(400*q.anim_time-60) * 64, 0, 64)", 0, 0]}, "rightLeg": {"rotation": ["-math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time*400) * 2,0 , 8)", 0]}, "right_leg_knee": {"rotation": ["math.clamp(-math.cos(400*q.anim_time-40) * 24, 0, 64)", 0, 0]}, "right_foot": {"rotation": ["math.clamp(-math.cos(400*q.anim_time-60) * 64, 0, 64)", 0, 0]}, "leftArm": {"rotation": ["-7.3712-math.cos(400*q.anim_time) * 24", 26.8465, -51.44504]}, "left_arm_knee": {"rotation": ["-52.5-math.cos(400*q.anim_time-40) * 12", 0, 0]}, "left_hand": {"rotation": ["-17.5-math.cos(400*q.anim_time-80) * 18", 0, 0]}, "rightArm": {"rotation": ["-2.45+math.cos(400*q.anim_time) * 24", -10.82497, 25.43399]}, "right_arm_knee": {"rotation": ["-45+math.cos(400*q.anim_time-40) * 26", 0, 0]}, "right_hook": {"rotation": ["-45+math.cos(400*q.anim_time-80) * 28", 0, 0]}, "head": {"rotation": ["math.cos(800*q.anim_time-80)*9", "math.sin(400*q.anim_time-80)*8", "math.cos(400*q.anim_time-80)*2"], "position": [0, "math.clamp(math.sin(800*q.anim_time-40)*0.5,-0.4,24)", 0]}, "left_eyelid": {"position": {"0.0": [0, 0, 0], "0.0833": [0, -1, 0], "0.3333": [0, -1, 0], "0.5": [0, 0, 0]}}, "left_ear": {"rotation": ["math.cos(800*q.anim_time-120)*12", 0, 0]}, "right_ear": {"rotation": ["math.cos(800*q.anim_time-120)*12", 0, 0]}, "torso": {"rotation": ["math.cos(800*q.anim_time-40)*2", "math.sin(400*q.anim_time-40)*8", "math.cos(400*q.anim_time-40)*2"], "position": [0, "math.clamp(math.sin(800*q.anim_time-10)*1,-0.4,24)", 0]}}, "sound_effects": {"0.3333": {"effect": "step"}, "0.8333": {"effect": "step"}}}, "animation.ithiefi_ditsh_foxy.run": {"loop": true, "animation_length": 0.8959, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 1.8))", "bones": {"root": {"position": [0, "3+math.sin(800*q.anim_time+120)*3 ", 0]}, "leftLeg": {"rotation": ["math.cos(400*q.anim_time) * 48", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time*400) * 2,0 , 8)", 0]}, "left_leg_knee": {"rotation": ["math.clamp(math.cos(400*q.anim_time-40) * 42, 0, 128)", 0, 0]}, "left_foot": {"rotation": ["math.clamp(math.cos(400*q.anim_time-60) * 64, 0, 128)", 0, 0]}, "rightLeg": {"rotation": ["-math.cos(400*q.anim_time) * 48", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time*400) * 2,0 , 8)", 0]}, "right_leg_knee": {"rotation": ["math.clamp(-math.cos(400*q.anim_time-40) * 42, 0, 128)", 0, 0]}, "right_foot": {"rotation": ["math.clamp(-math.cos(400*q.anim_time-60) * 64, 0, 128)", 0, 0]}, "leftArm": {"rotation": ["10.0659-math.cos(400*q.anim_time) * 48", 17.75755, -38.52307]}, "left_arm_knee": {"rotation": ["-52.5-math.cos(400*q.anim_time-70) * 24", 0, 0]}, "left_hand": {"rotation": ["-17.5-math.cos(400*q.anim_time-80) * 18", 0, 0]}, "rightArm": {"rotation": ["-37.1727+math.cos(400*q.anim_time) * 24", -15.94919, 140.0669]}, "right_arm_knee": {"rotation": ["20+math.cos(400*q.anim_time-40) * 26", 0, 0]}, "right_hook": {"rotation": ["42.5+math.cos(400*q.anim_time-80) * 28", 0, 0]}, "head": {"rotation": ["-7.5+math.cos(800*q.anim_time-80)*9", "math.sin(400*q.anim_time-80)*8", "math.cos(400*q.anim_time-80)*2"], "position": [0, "math.clamp(math.sin(800*q.anim_time-10)*1,-24,24)", 0]}, "torso": {"rotation": ["7.5+math.cos(800*q.anim_time-40)*2", "math.sin(400*q.anim_time-40)*12", "math.cos(400*q.anim_time-40)*2"], "position": [0, "math.clamp(math.sin(800*q.anim_time-10)*1,-0.4,24)", 0]}, "left_eyelid": {"position": {"0.0": [0, 0, 0], "0.0833": [0, -1, 0], "0.3333": [0, -1, 0], "0.5": [0, 0, 0]}}, "jaw": {"rotation": ["15+math.sin(800*q.anim_time-120)*12", 0, 0]}, "left_ear": {"rotation": ["math.cos(800*q.anim_time-120)*24", 0, 0]}, "right_ear": {"rotation": ["math.cos(800*q.anim_time-120)*24", 0, 0]}}, "sound_effects": {"0.4167": {"effect": "step"}, "0.8333": {"effect": "step"}}}, "animation.ithiefi_ditsh_foxy.attack": {"loop": "hold_on_last_frame", "animation_length": 0.4583, "bones": {"leftArm": {"rotation": {"0.0": [-5, 0, -25], "0.0417": [-52.5, 0, -25], "0.125": [-75.12, 7.09, -12.78], "0.1667": [-69.09, 23.14, 3.28], "0.2083": [-105.9, -35.16, 18.23], "0.2917": [-93.37, -44.72, 22.07], "0.3333": [-81.64, -54.7, 23.08], "0.4167": [-39.62, -27.97, -0.9], "0.4583": [-5, 0, -25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.2083": [0, -1, 3], "0.2917": [0, -0.75, 2.25], "0.3333": [0, -0.5, 1.5], "0.4167": [0, -0.25, 0.75], "0.4583": [0, 0, 0]}}, "rightArm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0417": [-77.38, 12.09, 1.83], "0.125": [-164.17, 7.21, -15.72], "0.1667": [-211.01, -13.22, -33.19], "0.25": [-48.19, 2.32, -61.96], "0.2917": [-38.19, 2.32, -61.96], "0.375": [-42.19, 17.39, 18.25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -3], "0.2917": [0, 0, -1.5], "0.375": [0, 0, 0]}}, "right_hook": {"rotation": {"0.0": [0, 0, 0], "0.25": [0, -180, 0]}}, "head": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [2.5, 0, 0], "0.125": [10.01, -5, -0.22], "0.1667": [10.01, -5, -0.22], "0.25": [-13.1, 7.73, -0.57], "0.2917": [-13.18, 25.33, -2.19], "0.375": [4.11, 27.32, 4.11], "0.4167": [2.83, 10.15, -2.21], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [7.5, 0, 0], "0.125": [2.53, 4.96, 0.65], "0.1667": [0.03, 4.96, 0.65], "0.25": [21.01, -8.45, 0.48], "0.2917": [28.92, -26.24, -9.15], "0.375": [13.92, -26.24, -9.15], "0.4167": [6.79, -10.25, 0.01], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}}}}}