import { system } from "@minecraft/server";
export function summonEntity(entityId, dimension, location) {
    dimension.spawnEntity(entityId, location);
    return;
}
export function spawnEntitiesWithInterval(dimension, entityConfigs, locationCallback, delay = 2, onEntitySpawned) {
    return new Promise((resolve) => {
        let totalToSpawn = 0;
        const entityCounts = new Map();
        entityConfigs.forEach((config) => {
            totalToSpawn += config.count;
            entityCounts.set(config.entityId, { current: 0, total: config.count });
        });
        let totalSpawned = 0;
        const spawnInterval = system.runInterval(() => {
            try {
                if (totalSpawned < totalToSpawn) {
                    let entityIdToSpawn = null;
                    for (const [entityId, counts] of entityCounts.entries()) {
                        if (counts.current < counts.total) {
                            entityIdToSpawn = entityId;
                            break;
                        }
                    }
                    if (entityIdToSpawn) {
                        const spawnPos = locationCallback();
                        if (spawnPos) {
                            const entity = dimension.spawnEntity(entityIdToSpawn, spawnPos);
                            if (onEntitySpawned && entity) {
                                onEntitySpawned(entity, entityIdToSpawn);
                            }
                            const counts = entityCounts.get(entityIdToSpawn);
                            if (counts) {
                                counts.current++;
                            }
                            totalSpawned++;
                        }
                    }
                    if (totalSpawned >= totalToSpawn) {
                        system.clearRun(spawnInterval);
                        resolve();
                    }
                }
            }
            catch (error) {
                console.warn(`Failed to spawn entity in interval: ${error}`);
                system.clearRun(spawnInterval);
                resolve();
            }
        }, delay);
    });
}
