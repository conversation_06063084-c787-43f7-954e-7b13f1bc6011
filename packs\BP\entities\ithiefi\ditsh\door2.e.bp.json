{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:door2", "is_spawnable": true, "is_summonable": true}, "component_groups": {}, "events": {}, "components": {"minecraft:type_family": {"family": ["inanimate", "door"]}, "minecraft:collision_box": {"width": 1, "height": 3}, "minecraft:custom_hit_test": {"hitboxes": [{"width": 1, "height": 3, "pivot": [0, 1.5, 0]}]}, "minecraft:persistent": {}, "minecraft:is_collidable": {}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"any_of": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:wooden_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:stone_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:iron_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:golden_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:diamond_axe"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:netherite_axe"}]}}, "deals_damage": "yes"}, {"on_damage": {"filters": [{"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:wooden_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:stone_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:iron_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:golden_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:diamond_axe", "operator": "not"}, {"test": "has_equipment", "subject": "other", "domain": "hand", "value": "minecraft:netherite_axe", "operator": "not"}]}, "deals_damage": "no"}]}, "minecraft:health": {"value": 40, "max": 40}, "minecraft:knockback_resistance": {"value": 1, "max": 1}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}}}}