import { Enti<PERSON>, GameMode, Player, Vector3, system } from "@minecraft/server";
import { getDistance, getRandomLocation } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

const TELEPORT_OFFSET_DISTANCE = 3;
const IDLE_TIMEOUT_TICKS = 8 * 20; // 8 seconds in ticks
const LOOK_DETECTION_RADIUS = 128;
const TELEPORT_COOLDOWN_TICKS = 3 * 20; // 3 seconds in ticks

// Raycast parameters for line-of-sight detection
const RAYCAST_LENGTH = 128; // Maximum raycast distance
const RAYCAST_STEP = 2; // Step size between raycast points
const RAYCAST_RADIUS = 5; // Radius around each raycast point to check for Specimen 6

// Track player idle states and last positions
const playerIdleTracker = new Map<string, {
  lastPosition: Vector3;
  lastMovementTick: number;
  isIdle: boolean;
}>();

// Track specimen 6 teleport cooldowns
const specimen6CooldownTracker = new Map<string, number>();

/**
 * Checks if a player is looking at Specimen 6 using fixed length raycast.
 * Uses 2-block step size and 5-block radius detection per raycast point.
 *
 * @param player - The player to check
 * @param specimen6Location - The location of Specimen 6
 * @returns True if the player is looking at Specimen 6
 */
function isPlayerLookingAtSpecimen6(player: Player, specimen6Location: Vector3): boolean {
  try {
    const playerLocation: Vector3 = player.location;
    const viewDirection: Vector3 = player.getViewDirection();

    // Check if Specimen 6 is within the player's field of view using raycast
    const raycastResults = fixedLenRaycast(
      playerLocation,
      viewDirection,
      RAYCAST_LENGTH,
      RAYCAST_STEP
    );

    // Check each raycast point for proximity to Specimen 6
    for (const raycastPoint of raycastResults) {
      const distanceToSpecimen6: number = getDistance(raycastPoint, specimen6Location);
      if (distanceToSpecimen6 <= RAYCAST_RADIUS) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.warn(`Failed to check if player is looking at Specimen 6: ${error}`);
    return false;
  }
}

/**
 * Updates player idle tracking based on their current position and movement.
 *
 * @param player - The player to track
 */
function updatePlayerIdleTracking(player: Player): void {
  const playerId: string = player.id;
  const currentTick: number = system.currentTick;
  const currentPosition: Vector3 = player.location;

  const existingTracker = playerIdleTracker.get(playerId);

  if (!existingTracker) {
    // Initialize tracking for new player
    playerIdleTracker.set(playerId, {
      lastPosition: currentPosition,
      lastMovementTick: currentTick,
      isIdle: false
    });
    return;
  }

  // Check if player has moved significantly (more than 0.5 blocks)
  const distanceMoved: number = getDistance(existingTracker.lastPosition, currentPosition);
  
  if (distanceMoved > 0.5) {
    // Player has moved, reset idle tracking
    existingTracker.lastPosition = currentPosition;
    existingTracker.lastMovementTick = currentTick;
    existingTracker.isIdle = false;
  } else {
    // Player hasn't moved, check if they've been idle long enough
    const ticksSinceLastMovement: number = currentTick - existingTracker.lastMovementTick;
    existingTracker.isIdle = ticksSinceLastMovement >= IDLE_TIMEOUT_TICKS;
  }
}

/**
 * Finds players who are not looking at Specimen 6 and are within range.
 *
 * @param specimen6 - The Specimen 6 entity
 * @returns Array of players not looking at Specimen 6
 */
function getPlayersNotLookingAtSpecimen6(specimen6: Entity): Player[] {
  const specimen6Location: Vector3 = specimen6.location;
  const playersNotLooking: Player[] = [];

  // Get all valid players within detection radius
  const players: Player[] = specimen6.dimension.getPlayers({
    location: specimen6Location,
    maxDistance: LOOK_DETECTION_RADIUS,
    excludeGameModes: [GameMode.Creative, GameMode.Spectator]
  });

  for (const player of players) {
    updatePlayerIdleTracking(player);
    
    if (!isPlayerLookingAtSpecimen6(player, specimen6Location)) {
      playersNotLooking.push(player);
    }
  }

  return playersNotLooking;
}

/**
 * Finds players who have been idle for more than 8 seconds.
 *
 * @param specimen6 - The Specimen 6 entity
 * @returns Array of idle players
 */
function getIdlePlayers(specimen6: Entity): Player[] {
  const specimen6Location: Vector3 = specimen6.location;
  const idlePlayers: Player[] = [];

  // Get all valid players within a larger radius for idle detection
  const players: Player[] = specimen6.dimension.getPlayers({
    location: specimen6Location,
    maxDistance: 256,
    excludeGameModes: [GameMode.Creative, GameMode.Spectator]
  });

  for (const player of players) {
    updatePlayerIdleTracking(player);
    
    const tracker = playerIdleTracker.get(player.id);
    if (tracker?.isIdle) {
      idlePlayers.push(player);
    }
  }

  return idlePlayers;
}

/**
 * Teleports Specimen 6 behind a player who is not looking at him.
 * This function is called by the environment sensor trigger.
 *
 * @param specimen6 - The Specimen 6 entity
 */
export function specimen6TeleportBehindPlayer(specimen6: Entity): void {
  try {
    const specimen6Id: string = specimen6.id;
    const currentTick: number = system.currentTick;

    // Check cooldown
    const lastTeleportTick = specimen6CooldownTracker.get(specimen6Id) || 0;
    if (currentTick - lastTeleportTick < TELEPORT_COOLDOWN_TICKS) {
      return;
    }

    const playersNotLooking: Player[] = getPlayersNotLookingAtSpecimen6(specimen6);
    
    if (playersNotLooking.length === 0) {
      return;
    }

    // Select a random player not looking at Specimen 6
    const targetPlayer: Player = playersNotLooking[Math.floor(Math.random() * playersNotLooking.length)]!;
    const playerViewDirection: Vector3 = targetPlayer.getViewDirection();

    // Try multiple teleportation strategies
    let finalTeleportLocation: Vector3 | undefined;

    // Strategy 1: Teleport behind the player (opposite to view direction)
    const behindPlayerLocation: Vector3 = {
      x: targetPlayer.location.x - (playerViewDirection.x * TELEPORT_OFFSET_DISTANCE),
      y: targetPlayer.location.y,
      z: targetPlayer.location.z - (playerViewDirection.z * TELEPORT_OFFSET_DISTANCE)
    };

    const behindPlayerBlock = specimen6.dimension.getBlock(behindPlayerLocation);
    if (behindPlayerBlock?.isAir) {
      finalTeleportLocation = behindPlayerLocation;
    }

    // Strategy 2: Try random locations around the player if behind fails
    if (!finalTeleportLocation) {
      finalTeleportLocation = getRandomLocation(
        targetPlayer.location,
        specimen6.dimension,
        2, // baseOffset: 2 blocks minimum distance
        4, // additionalOffset: up to 6 blocks total distance (2+4)
        0, // randomYOffset: same Y level as player
        true // checkForAirBlock: ensure safe teleportation
      );
    }

    // Strategy 3: Final fallback - teleport near player
    if (!finalTeleportLocation) {
      finalTeleportLocation = {
        x: targetPlayer.location.x + (Math.random() - 0.5) * 4,
        y: targetPlayer.location.y,
        z: targetPlayer.location.z + (Math.random() - 0.5) * 4
      };
    }

    // Teleport Specimen 6
    specimen6.teleport(finalTeleportLocation);

    // Play step sound effect
    specimen6.dimension.playSound("mob.ditsh.specimen6.step", finalTeleportLocation);

    // Update cooldown
    specimen6CooldownTracker.set(specimen6Id, currentTick);

  } catch (error) {
    console.warn(`Failed to teleport Specimen 6 behind player: ${error}`);
  }
}

/**
 * Teleports Specimen 6 to an idle player (idle for more than 8 seconds).
 * This function is called by the environment sensor trigger.
 *
 * @param specimen6 - The Specimen 6 entity
 */
export function specimen6TeleportToIdlePlayer(specimen6: Entity): void {
  try {
    const specimen6Id: string = specimen6.id;
    const currentTick: number = system.currentTick;

    // Check cooldown
    const lastTeleportTick = specimen6CooldownTracker.get(specimen6Id) || 0;
    if (currentTick - lastTeleportTick < TELEPORT_COOLDOWN_TICKS) {
      return;
    }

    const idlePlayers: Player[] = getIdlePlayers(specimen6);
    
    if (idlePlayers.length === 0) {
      return;
    }

    // Select the nearest idle player
    let nearestIdlePlayer: Player = idlePlayers[0]!;
    let nearestDistance: number = getDistance(specimen6.location, nearestIdlePlayer.location);

    for (const player of idlePlayers) {
      const distance: number = getDistance(specimen6.location, player.location);
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestIdlePlayer = player;
      }
    }

    // Try teleportation strategies similar to behind player
    let finalTeleportLocation: Vector3 | undefined;

    // Strategy 1: Try teleporting near the idle player
    finalTeleportLocation = getRandomLocation(
      nearestIdlePlayer.location,
      specimen6.dimension,
      3, // baseOffset: 3 blocks minimum distance
      5, // additionalOffset: up to 8 blocks total distance (3+5)
      0, // randomYOffset: same Y level as player
      true // checkForAirBlock: ensure safe teleportation
    );

    // Strategy 2: Final fallback - teleport directly to player location
    if (!finalTeleportLocation) {
      finalTeleportLocation = {
        x: nearestIdlePlayer.location.x,
        y: nearestIdlePlayer.location.y,
        z: nearestIdlePlayer.location.z
      };
    }

    // Teleport Specimen 6
    specimen6.teleport(finalTeleportLocation);

    // Play step sound effect
    specimen6.dimension.playSound("mob.ditsh.specimen6.step", finalTeleportLocation);

    // Update cooldown
    specimen6CooldownTracker.set(specimen6Id, currentTick);

  } catch (error) {
    console.warn(`Failed to teleport Specimen 6 to idle player: ${error}`);
  }
}

/**
 * Cleanup function to remove expired player tracking entries.
 * Should be called periodically to prevent memory leaks.
 */
export function cleanupSpecimen6PlayerTracking(): void {
  const currentTick: number = system.currentTick;
  const expiredEntries: string[] = [];

  for (const [playerId, tracker] of playerIdleTracker.entries()) {
    // Remove entries older than 5 minutes (6000 ticks)
    if (currentTick - tracker.lastMovementTick > 6000) {
      expiredEntries.push(playerId);
    }
  }

  for (const playerId of expiredEntries) {
    playerIdleTracker.delete(playerId);
  }

  // Also cleanup teleport cooldown tracker
  const expiredCooldowns: string[] = [];
  for (const [specimen6Id, lastTeleportTick] of specimen6CooldownTracker.entries()) {
    if (currentTick - lastTeleportTick > TELEPORT_COOLDOWN_TICKS * 2) {
      expiredCooldowns.push(specimen6Id);
    }
  }

  for (const specimen6Id of expiredCooldowns) {
    specimen6CooldownTracker.delete(specimen6Id);
  }
}
