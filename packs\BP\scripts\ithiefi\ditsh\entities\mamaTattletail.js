import { GameMode } from "@minecraft/server";
const TELEPORT_RANGE = 20;
const TELEPORT_CHANCE = 2;
export function mamaTattletailTeleportHandler(mamaTattletail) {
    if (Math.floor(Math.random() * TELEPORT_RANGE) + 1 <= TELEPORT_CHANCE) {
        const location = mamaTattletail.location;
        const players = mamaTattletail.dimension.getPlayers({ location: location, maxDistance: 256, excludeGameModes: [GameMode.Creative, GameMode.Spectator] });
        const nearestPlayer = players[0];
        if (nearestPlayer) {
            const calculatedTpLocation = {
                x: nearestPlayer.location.x + (nearestPlayer.getViewDirection().x * 2),
                y: nearestPlayer.location.y,
                z: nearestPlayer.location.z + (nearestPlayer.getViewDirection().z * 2)
            };
            const targetBlock = mamaTattletail.dimension.getBlock(calculatedTpLocation);
            let finalTpLocation;
            if (targetBlock && targetBlock.type.id === "minecraft:air") {
                finalTpLocation = calculatedTpLocation;
            }
            else {
                finalTpLocation = nearestPlayer.location;
            }
            mamaTattletail.teleport(finalTpLocation);
        }
        for (const player of players) {
            player.addEffect("blindness", 60, { amplifier: 1, showParticles: false });
        }
    }
}
