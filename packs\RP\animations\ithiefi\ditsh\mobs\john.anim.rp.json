{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_john.idle": {"loop": true, "animation_length": 3.5834, "bones": {"leftArm": {"rotation": [0, 0, "-5+math.sin(100*q.anim_time) * 3"], "position": [0, "math.sin(100*q.anim_time-80) * 0.5", 0]}, "rightArm": {"rotation": [0, 0, "5-math.sin(100*q.anim_time) * 3"], "position": [0, "math.sin(100*q.anim_time-80) * 0.5", 0]}, "head": {"rotation": [0, 0, "10+math.sin(100*q.anim_time-40) * 8"]}, "torso": {"position": [0, "-1+math.sin(100*q.anim_time-120) * 0.4", 0]}}}, "animation.ithiefi_ditsh_john.walk": {"loop": true, "animation_length": 0.8959, "anim_time_update": "q.anim_time + (q.delta_time * (q.modified_move_speed * 2.4))", "bones": {"head": {"rotation": ["math.cos(800*q.anim_time-80) * 8", 0, 0]}, "leftArm": {"rotation": ["-34.4899+math.cos(800*q.anim_time-80) * 4", -26.1442, 0.95088], "position": [0, -1, 0]}, "rightArm": {"rotation": ["-35+math.cos(800*q.anim_time-40) * 4", 0, 0]}, "leftLeg": {"rotation": ["math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(math.sin(q.anim_time* 400) * 2,0 , 8)", 0]}, "rightLeg": {"rotation": ["-math.cos(400*q.anim_time) * 24", 0, 0], "position": [0, "math.clamp(-math.sin(q.anim_time* 400) * 2,0 , 8)", 0]}, "root": {"position": [0, "math.sin(800*q.anim_time-80) * 0.75", 0]}, "torso": {"rotation": ["math.cos(q.anim_time * 800) * 1", 0, 0], "position": [0, "-1+math.sin(q.anim_time * 800) * 0.3", 0]}}}, "animation.ithiefi_ditsh_john.attack": {"loop": "hold_on_last_frame", "animation_length": 0.4583, "bones": {"head": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [2.5, 0, 0], "0.125": [10.01, -5, -0.22], "0.1667": [10.01, -5, -0.22], "0.25": [-13.1, 7.73, -0.57], "0.2917": [-13.18, 25.33, -2.19], "0.375": [4.11, 27.32, 4.11], "0.4167": [2.83, 10.15, -2.21], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}, "leftArm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0417": [-77.38, 12.09, 1.83], "0.125": [-164.17, 7.21, -15.72], "0.1667": [-211.01, -13.22, -33.19], "0.25": [-48.19, 2.32, -61.96], "0.2917": [-38.19, 2.32, -61.96], "0.375": [-42.19, 17.39, 18.25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -3], "0.2917": [0, 0, -1.5], "0.375": [0, 0, 0]}}, "rightArm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0417": [-77.38, 12.09, 1.83], "0.125": [-164.17, 7.21, -15.72], "0.1667": [-211.01, -13.22, -33.19], "0.25": [-48.19, 2.32, -61.96], "0.2917": [-38.19, 2.32, -61.96], "0.375": [-42.19, 17.39, 18.25]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, -3], "0.2917": [0, 0, -1.5], "0.375": [0, 0, 0]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0417": [7.5, 0, 0], "0.125": [2.53, 4.96, 0.65], "0.1667": [0.03, 4.96, 0.65], "0.25": [21.01, -8.45, 0.48], "0.2917": [28.92, -26.24, -9.15], "0.375": [13.92, -26.24, -9.15], "0.4167": [6.79, -10.25, 0.01], "0.4583": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.125": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.2917": [0, 0, 0], "0.375": [0, 0, 0], "0.4167": [0, 0, 0], "0.4583": [0, 0, 0]}}}}}}