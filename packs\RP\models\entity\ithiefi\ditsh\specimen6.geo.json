{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.skeleton.v1.8", "texture_width": 64, "texture_height": 32, "visible_bounds_width": 2, "visible_bounds_height": 3, "visible_bounds_offset": [0, 1.5, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "torso", "parent": "root", "pivot": [0, 12, 0]}, {"name": "head", "parent": "torso", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "body", "parent": "torso", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}]}, {"name": "leftArm", "parent": "torso", "pivot": [5, 22, 0], "mirror": true, "cubes": [{"origin": [4, 12, -1], "size": [2, 12, 2], "uv": [40, 16]}]}, {"name": "leftItem", "parent": "leftArm", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "torso", "pivot": [-5, 22, 0], "cubes": [{"origin": [-6, 12, -1], "size": [2, 12, 2], "uv": [40, 16]}]}, {"name": "rightItem", "parent": "rightArm", "pivot": [-6, 15, 1]}, {"name": "waist", "parent": "root", "pivot": [0, 12, 0]}, {"name": "leftLeg", "parent": "waist", "pivot": [2, 12, 0], "mirror": true, "cubes": [{"origin": [1, 0, -1], "size": [2, 12, 2], "uv": [0, 16]}]}, {"name": "rightLeg", "parent": "waist", "pivot": [-2, 12, 0], "cubes": [{"origin": [-3, 0, -1], "size": [2, 12, 2], "uv": [0, 16]}]}]}]}