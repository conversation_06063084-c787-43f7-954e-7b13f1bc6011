{"format_version": "1.19.0", "animation_controllers": {"controller.animation.ithiefi_ditsh_scp096.general": {"states": {"default": {"animations": ["sit"], "transitions": [{"rage": "q.property('ditsh:looked_at') == true"}, {"attack": "v.attack_time > 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "rage": {"animations": ["rage"], "transitions": [{"idle": "q.all_animations_finished || q.has_target"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "idle": {"animations": ["idle"], "transitions": [{"running": "q.ground_speed > 0.3"}, {"attack": "v.attack_time > 0"}, {"default": "q.property('ditsh:looked_at') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "running": {"animations": ["run"], "transitions": [{"idle": "q.ground_speed < 0.3"}, {"attack": "v.attack_time > 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "attack": {"animations": ["attack"], "transitions": [{"idle": "q.ground_speed < 0.3 && v.attack_time <= 0"}, {"running": "q.ground_speed > 0.3 && v.attack_time <= 0"}], "blend_transition": 0.3, "blend_via_shortest_path": true}}}}}