{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ditsh:sonic", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ithiefi/ditsh/entities/sonic/sonic"}, "geometry": {"default": "geometry.ithiefi_ditsh_sonic"}, "animations": {"look_at_target": "animation.common.look_at_target", "idle": "animation.ithiefi_ditsh_sonic.idle", "walk": "animation.ithiefi_ditsh_sonic.walk", "run": "animation.ithiefi_ditsh_sonic.run", "attack": "animation.ithiefi_ditsh_sonic.attack", "move": "controller.animation.ithiefi_ditsh_sonic.move", "general": "controller.animation.ithiefi_ditsh_sonic.general"}, "scripts": {"animate": [{"look_at_target": "q.is_alive"}, "general"]}, "render_controllers": ["controller.render.default"], "spawn_egg": {"base_color": "#2d2a7f", "overlay_color": "#e05353"}}}}