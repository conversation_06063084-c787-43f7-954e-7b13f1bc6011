{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ithiefi_ditsh_foxy", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 4, "visible_bounds_height": 4.5, "visible_bounds_offset": [0, 1.75, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "torso", "parent": "root", "pivot": [0, 21, 0]}, {"name": "head", "parent": "torso", "pivot": [0, 32, 0], "cubes": [{"origin": [-4, 34, -3], "size": [8, 7, 6], "uv": [2, 2]}, {"origin": [-1, 41, 1], "size": [2, 4, 0], "pivot": [0, 41, 1], "rotation": [-20, 0, 0], "uv": [30, 8]}, {"origin": [-1, 41.34202, 0.93969], "size": [2, 4, 0], "pivot": [0, 41, 0], "rotation": [-20, -90, 0], "uv": [30, 8]}, {"origin": [-1, 41.34202, 0.93969], "size": [2, 4, 0], "pivot": [0, 41, 0], "rotation": [-20, 90, 0], "uv": [30, 8]}, {"origin": [-4, 36.5, -3], "size": [5, 5, 6], "inflate": 0.1, "uv": [58, 2]}, {"origin": [0.5, 37, -3], "size": [3, 2, 2], "uv": [26, 41]}, {"origin": [-7, 34, -3], "size": [3, 3, 2], "uv": [46, 8], "mirror": true}, {"origin": [4, 34, -3], "size": [3, 3, 2], "uv": [46, 8]}, {"origin": [-2, 34, -8], "size": [4, 3, 5], "uv": [0, 48]}, {"origin": [-1, 36.5, -8.5], "size": [2, 1, 1], "uv": [0, 18]}]}, {"name": "left_eyelid", "parent": "head", "pivot": [2, 40.5, -3], "cubes": [{"origin": [1, 38.6, -2.9], "size": [2, 2, 0], "uv": [0, 31]}]}, {"name": "left_eye", "parent": "head", "pivot": [2, 38, -1.5], "cubes": [{"origin": [1, 37, -2.65], "size": [2, 2, 0.3], "inflate": -0.15, "uv": [30, 21]}, {"origin": [1, 37, -0.65], "size": [2, 2, 0.3], "inflate": -0.15, "uv": [24, 4]}, {"origin": [1.5, 37.5, -0.6], "size": [1, 1, 0.3], "inflate": -0.15, "uv": [0, 5]}, {"origin": [1, 37, -2.95], "size": [2, 2, 0.8], "inflate": -0.4, "uv": [24, 2]}]}, {"name": "jaw", "parent": "head", "pivot": [0, 34, -1], "cubes": [{"origin": [-2.5, 32, -8], "size": [5, 2, 5], "uv": [45, 43]}, {"origin": [-3.5, 32, -3], "size": [7, 2, 3], "uv": [29, 12]}, {"origin": [-2, 33.9, -7.75], "size": [4, 1, 6], "inflate": -0.1, "uv": [30, 41]}]}, {"name": "left_ear", "parent": "head", "pivot": [4, 40, 0.5], "rotation": [0, 0, 30], "cubes": [{"origin": [2.29289, 41.20711, -0.5], "size": [3, 3, 2], "uv": [15, 31]}, {"origin": [3.29289, 44.20711, -0.5], "size": [1, 1, 2], "uv": [0, 15]}, {"origin": [3.29289, 39.20711, 0.5], "size": [1, 2, 0], "uv": [0, 0]}]}, {"name": "right_ear", "parent": "head", "pivot": [-4, 40, 0.5], "rotation": [0, 0, -30], "cubes": [{"origin": [-5.29289, 41.20711, -0.5], "size": [3, 3, 2], "uv": [15, 31], "mirror": true}, {"origin": [-4.29289, 44.20711, -0.5], "size": [1, 1, 2], "uv": [0, 15], "mirror": true}, {"origin": [-4.29289, 39.20711, 0.5], "size": [1, 2, 0], "uv": [0, 0], "mirror": true}]}, {"name": "body", "parent": "torso", "pivot": [0, 22, 0], "cubes": [{"origin": [-4, 22, -2.5], "size": [8, 8, 5], "uv": [25, 27]}, {"origin": [-1, 21, -1], "size": [2, 10, 2], "uv": [50, 50]}, {"origin": [4.5, 30.5, -2], "size": [2, 1, 4], "pivot": [4.5, 30, 1], "rotation": [0, 0, -20], "uv": [47, 37]}, {"origin": [-6.5, 30.5, -2], "size": [2, 1, 4], "pivot": [-4.5, 30, 1], "rotation": [0, 0, 20], "uv": [47, 37], "mirror": true}, {"origin": [-1, 31, -1], "size": [2, 3, 2], "uv": [0, 0]}, {"origin": [-4.5, 21, -3], "size": [9, 10, 6], "uv": [0, 15]}]}, {"name": "leftArm", "parent": "torso", "pivot": [5, 30, 0], "cubes": [{"origin": [3.6, 23.5, -1.5], "size": [3, 5, 3], "inflate": 0.2, "uv": [38, 50]}, {"origin": [4, 22.5, -1], "size": [2, 6, 2], "uv": [50, 50]}, {"origin": [4, 28.5, -1], "size": [2, 2, 2], "uv": [51, 29]}]}, {"name": "left_arm_knee", "parent": "leftArm", "pivot": [5, 22.5, 0], "cubes": [{"origin": [3.5, 16.5, -1.5], "size": [3, 6, 3], "uv": [49, 14]}, {"origin": [4.5, 15.5, -0.5], "size": [1, 7, 1], "uv": [52, 51]}]}, {"name": "left_hand", "parent": "left_arm_knee", "pivot": [5, 15.5, 0], "cubes": [{"origin": [3.5, 12.5, -1.5], "size": [3, 3, 3], "uv": [51, 23]}]}, {"name": "rightArm", "parent": "torso", "pivot": [-5, 30, 0], "cubes": [{"origin": [-6.4, 23.5, -1.5], "size": [3, 5, 3], "inflate": 0.2, "uv": [38, 50], "mirror": true}, {"origin": [-6, 22.5, -1], "size": [2, 1, 2], "uv": [0, 5], "mirror": true}, {"origin": [-6, 28.5, -1], "size": [2, 2, 2], "uv": [51, 29], "mirror": true}]}, {"name": "right_arm_knee", "parent": "rightArm", "pivot": [-5, 22.5, 0], "cubes": [{"origin": [-6.5, 16.5, -1.5], "size": [3, 6, 3], "uv": [49, 14], "mirror": true}, {"origin": [-5.5, 15.5, -0.5], "size": [1, 7, 1], "uv": [52, 51], "mirror": true}]}, {"name": "right_hook", "parent": "right_arm_knee", "pivot": [-5, 15.5, 0], "cubes": [{"origin": [-6.5, 14.75, -1.5], "size": [3, 1, 3], "inflate": 0.2, "uv": [24, 17], "mirror": true}, {"origin": [-5, 7.75, -3.5], "size": [0, 7, 7], "uv": [0, 34], "mirror": true}]}, {"name": "waist", "parent": "root", "pivot": [0, 20, 0], "cubes": [{"origin": [-4.5, 19, -3], "size": [9, 2, 6], "uv": [24, 0]}]}, {"name": "leftLeg", "parent": "waist", "pivot": [3, 19, 0], "cubes": [{"origin": [1, 10, -2], "size": [4, 8, 4], "uv": [14, 41]}, {"origin": [2, 18, -1], "size": [2, 1, 2], "uv": [0, 5]}, {"origin": [2, 12, -1], "size": [2, 6, 2], "uv": [50, 50]}]}, {"name": "left_leg_knee", "parent": "leftLeg", "pivot": [3, 10.5, 0], "cubes": [{"origin": [2, 2, -1], "size": [2, 10, 2], "uv": [50, 50]}]}, {"name": "left_foot", "parent": "left_leg_knee", "pivot": [3, 2.5, 0], "cubes": [{"origin": [0, -0.5, -5], "size": [6, 2, 2], "uv": [0, 56]}, {"origin": [0, -0.5, -3], "size": [6, 3, 6], "uv": [30, 17]}]}, {"name": "rightLeg", "parent": "waist", "pivot": [-3, 19, 0], "cubes": [{"origin": [-5, 10, -2], "size": [4, 8, 4], "uv": [14, 41], "mirror": true}, {"origin": [-4, 18, -1], "size": [2, 1, 2], "uv": [0, 5], "mirror": true}, {"origin": [-4, 12, -1], "size": [2, 6, 2], "uv": [50, 50], "mirror": true}]}, {"name": "right_leg_knee", "parent": "rightLeg", "pivot": [-3, 10.5, 0], "cubes": [{"origin": [-4, 2, -1], "size": [2, 10, 2], "uv": [50, 50], "mirror": true}]}, {"name": "right_foot", "parent": "right_leg_knee", "pivot": [-3, 2.5, 0], "cubes": [{"origin": [-6, -0.5, -5], "size": [6, 2, 2], "uv": [0, 56], "mirror": true}, {"origin": [-6, -0.5, -3], "size": [6, 3, 6], "uv": [30, 17], "mirror": true}]}]}]}