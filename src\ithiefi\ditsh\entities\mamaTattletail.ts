import { Entity, GameMode } from "@minecraft/server";

const TELEPORT_RANGE = 20;
const TELEPORT_CHANCE = 2;

/**
 * Handles the teleportation behavior for Mama Tattletail
 *
 * @param mamaTattletail - The Mama Tattletail entity
 */
export function mamaTattletailTeleportHandler(mamaTattletail: Entity) {
    // Roll for teleportation chance
    if (Math.floor(Math.random() * TELEPORT_RANGE) + 1 <= TELEPORT_CHANCE) {
        const location = mamaTattletail.location;

        // Get all valid players in the vicinity
        const players = mamaTattletail.dimension.getPlayers({location: location, maxDistance: 256, excludeGameModes: [GameMode.Creative, GameMode.Spectator]});
        const nearestPlayer = players[0]

        // Teleport to the nearest player if any are found
        if (nearestPlayer) {
            // Calculate the intended teleport location (2 blocks in front of the player)
            const calculatedTpLocation = {
                x: nearestPlayer.location.x + (nearestPlayer.getViewDirection().x * 2),
                y: nearestPlayer.location.y,
                z: nearestPlayer.location.z + (nearestPlayer.getViewDirection().z * 2)
            };

            // Check if the calculated location contains an air block to avoid teleporting into solid blocks
            const targetBlock = mamaTattletail.dimension.getBlock(calculatedTpLocation);
            let finalTpLocation;

            if (targetBlock && targetBlock.type.id === "minecraft:air") {
                // Target location is air, safe to teleport there
                finalTpLocation = calculatedTpLocation;
            } else {
                // Target location is not air or block couldn't be retrieved, fallback to player location
                finalTpLocation = nearestPlayer.location;
            }

            mamaTattletail.teleport(finalTpLocation);
        }

        // Apply darkness effect for 2 seconds for all valid players
        for (const player of players) {
            player.addEffect("blindness", 60, {amplifier: 1, showParticles: false});
        }
    }
}