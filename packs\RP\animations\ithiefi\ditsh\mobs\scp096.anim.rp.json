{"format_version": "1.8.0", "animations": {"animation.ithiefi_ditsh_scp096.idle": {"loop": true, "bones": {"body": {"rotation": [0, "Math.cos(query.life_time * 180.0) *3", 0], "position": [0, "-3.8+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0]}, "body2": {"rotation": ["10+<PERSON>.cos(query.life_time * 180.0) *3", 0, 0]}, "body3": {"rotation": [35, 0, 0]}, "bone": {"rotation": [-5, 0, 0], "position": [0, 1, 0.1]}, "bone2": {"rotation": [0, 0, 0], "position": [0, 0, 0]}, "bone5": {"rotation": [-10, 0, 0]}, "bone6": {"rotation": [15, 0, 0]}, "bone7": {"rotation": [-10, 0, 0]}, "bone8": {"rotation": [0, 0, 0]}, "leftleg": {"rotation": [-35, 0, 0], "position": [0, "-3.8+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0]}, "leftleg2": {"rotation": [70, 0, 0], "position": [0, "Math.cos(query.life_time * 180.0) *0.3", 0]}, "leftleg3": {"rotation": [-35, 0, 0]}, "rightleg": {"rotation": [-35, 0, 0], "position": [0, "-3.8+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0]}, "rightleg2": {"rotation": [70, 0, 0], "position": [0, "Math.cos(query.life_time * 180.0) *0.3", 0]}, "rightleg3": {"rotation": [-35, 0, 0]}, "leftarm": {"rotation": ["-50+<PERSON>.cos(query.life_time * 180.0) *-3", -5, "Math.cos(query.life_time * 180.0) *-3"]}, "leftarm2": {"rotation": [-15, 0, 0]}, "leftarm3": {"rotation": [0, 90, "0+<PERSON>.cos(query.life_time * 180.0) *3"]}, "leftarm4": {"rotation": [0, 0, "95+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "leftarm9": {"rotation": [0, 0, 50]}, "leftarm5": {"rotation": [0, 0, "70+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "leftarm10": {"rotation": [0, 0, 35]}, "leftarm6": {"rotation": [0, 0, "40+Math.cos(query.life_time * 180.0) *-3"]}, "leftarm11": {"rotation": [0, 0, 40]}, "leftarm7": {"rotation": [0, 0, "25+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "leftarm12": {"rotation": [0, 0, 45]}, "leftarm8": {"rotation": [0, 0, "25+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "leftarm13": {"rotation": [0, 0, 45]}, "rightarm": {"rotation": ["-50+<PERSON>.cos(query.life_time * 180.0) *3", 5, "Math.cos(query.life_time * 180.0) *3"]}, "rightarm2": {"rotation": [-15, 0, 0]}, "rightarm3": {"rotation": [0, -90, "0+<PERSON>.cos(query.life_time * 180.0) *3"]}, "rightarm4": {"rotation": [0, 0, "-60+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "rightarm5": {"rotation": [0, 0, -45]}, "rightarm6": {"rotation": [0, 0, "-35+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "rightarm7": {"rotation": [0, 0, -40]}, "rightarm8": {"rotation": [0, 0, "-20+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "rightarm9": {"rotation": [0, 0, -30]}, "rightarm10": {"rotation": [0, 0, "-5+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "rightarm11": {"rotation": [0, 0, -35]}, "rightarm12": {"rotation": [0, 0, "-30+<PERSON>.cos(query.life_time * 180.0) *-3"]}, "rightarm13": {"rotation": [0, 0, -50]}, "neck": {"rotation": [-10, 0, 0], "scale": 0.8}, "head": {"rotation": ["-40+query.target_x_rotation - this", "query.target_y_rotation - this", "Math.cos(query.life_time * 180.0) *3"]}, "jaw": {"rotation": ["1+Math.cos(query.life_time * 180.0) *-1", 0, 0]}}}, "animation.ithiefi_ditsh_scp096.run": {"loop": true, "anim_time_update": "query.modified_distance_moved", "bones": {"body2": {"rotation": [20, 0, 0]}, "body3": {"rotation": [-20, 0, 0]}, "leftarm": {"rotation": ["-70+<PERSON>.cos(query.anim_time * 40 + 40) *5", -30, 0]}, "leftarm2": {"rotation": [-30, 0, 0]}, "leftarm3": {"rotation": [-30, 40, -40]}, "leftarm4": {"rotation": [15, 0, -20]}, "leftarm9": {"rotation": [0, 0, 33]}, "leftarm5": {"rotation": [5, 0, -22]}, "leftarm10": {"rotation": [0, 0, 46]}, "leftarm6": {"rotation": [-10, 0, -15]}, "leftarm11": {"rotation": [0, 0, 32]}, "leftarm7": {"rotation": [-20, 0, -15]}, "leftarm12": {"rotation": [0, 0, 38]}, "leftarm8": {"rotation": [-30, 0, -25]}, "leftarm13": {"rotation": [0, 0, 30]}, "rightarm": {"rotation": ["-75+<PERSON>.cos(query.anim_time * 40 + 40) *5", 25, 0]}, "rightarm2": {"rotation": [-10, 0, 0]}, "rightarm3": {"rotation": [-30, -40, 40]}, "rightarm4": {"rotation": [15, 0, 25]}, "rightarm5": {"rotation": [0, 0, -30]}, "rightarm6": {"rotation": [10, 0, 25]}, "rightarm7": {"rotation": [0, 0, -25]}, "rightarm8": {"rotation": [-5, 0, 20]}, "rightarm9": {"rotation": [0, 0, -20]}, "rightarm10": {"rotation": [-15, 0, 15]}, "rightarm11": {"rotation": [0, 0, -20]}, "rightarm12": {"rotation": [-20, 0, 25]}, "rightarm13": {"rotation": [0, 0, -30]}, "jaw": {"rotation": [0, 0, 0], "scale": 1}, "leftleg": {"rotation": ["Math.cos(query.anim_time * 40 + 40) *50.0", 0, 0]}, "rightleg": {"rotation": ["Math.cos(query.anim_time * 40 + 40) *-50.0", 0, 0]}, "neck": {"rotation": ["Math.cos(query.life_time * 180.0) *5", 0, 0]}, "leftleg2": {"rotation": [15, 0, 0]}, "rightleg2": {"rotation": [15, 0, 0]}, "head": {"rotation": [0, 0, 0]}}}, "animation.ithiefi_ditsh_scp096.sit": {"loop": true, "bones": {"body": {"position": [0, -19.7, 0]}, "body2": {"rotation": ["-5+<PERSON>.cos(query.life_time * 180.0) *3", 0, 0]}, "body3": {"rotation": ["25+<PERSON>.cos(query.life_time * 180.0) *-3", 0, 0]}, "leftarm": {"rotation": ["-75+<PERSON>.cos(query.life_time * 180.0) *-3", 10, 0]}, "leftarm2": {"rotation": ["-10+<PERSON>.cos(query.life_time * 180.0) *3", 0, 15]}, "leftarm3": {"rotation": [65, "55+<PERSON>.cos(query.life_time * 180.0) *-3", 60]}, "leftarm4": {"rotation": [15, 0, 20]}, "leftarm9": {"rotation": [0, 0, 50]}, "leftarm5": {"rotation": [5, 0, 15]}, "leftarm10": {"rotation": [0, 0, 40]}, "leftarm6": {"rotation": [-5, 0, 0]}, "leftarm11": {"rotation": [0, 0, 65]}, "leftarm7": {"rotation": [-15, 0, 10]}, "leftarm12": {"rotation": [0, 0, 55]}, "leftarm8": {"rotation": [-10, 0, -10]}, "leftarm13": {"rotation": [0, 0, 50]}, "rightarm": {"rotation": ["-50+<PERSON>.cos(query.life_time * 180.0) *-3", 0, 0]}, "rightarm2": {"rotation": ["-50+<PERSON>.cos(query.life_time * 180.0) *3", 0, -35]}, "rightarm3": {"rotation": [75, "-75+<PERSON>.cos(query.life_time * 180.0) *-3", -40]}, "rightarm4": {"rotation": [10, 0, -35]}, "rightarm5": {"rotation": [0, 0, -55]}, "rightarm6": {"rotation": [5, 0, -55]}, "rightarm7": {"rotation": [0, 0, -40]}, "rightarm9": {"rotation": [0, 0, -45]}, "rightarm10": {"rotation": [-5, 0, -55]}, "rightarm11": {"rotation": [0, 0, -15]}, "rightarm12": {"rotation": [-5, 0, -40]}, "rightarm13": {"rotation": [0, 0, -20]}, "neck": {"rotation": ["55+<PERSON>.cos(query.life_time * 180.0) *-3", 0, 0], "scale": [0.8, 1, 0.8]}, "head": {"rotation": ["10+<PERSON>.cos(query.life_time * 180.0) *-3", 0, 0]}, "leftleg": {"rotation": [-149, "0+<PERSON>.cos(query.life_time * 180.0) *-3", 0], "position": [0, -19, 0]}, "leftleg2": {"rotation": [100, 0, 0], "scale": 1}, "leftleg3": {"rotation": [50, 0, 0]}, "rightleg": {"rotation": [-149, "0+<PERSON>.cos(query.life_time * 180.0) *3", 0], "position": [0, -19, 0]}, "rightleg2": {"rotation": [100, 0, 0]}, "rightleg3": {"rotation": [50, 0, 0]}, "rightarm8": {"rotation": [0, 0, -50]}}}, "animation.ithiefi_ditsh_scp096.rage": {"loop": "hold_on_last_frame", "animation_length": 30, "bones": {"body": {"rotation": {"0.0": [0, 0, 0], "1.0": [0, 0, 0], "8.5167": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -5, 0], "8.5167": [0, -5, 0], "9.1667": [0, 0, 0]}}, "body2": {"rotation": {"0.0": [0, 0, 0], "1.0": ["80+Math.cos(query.life_time * 180) *1", 0, 0], "9.1667": [80, 0, 0], "10.5667": ["Math.cos(query.anim_time * -40  -70) *-20.0", "Math.cos(query.life_time * 100) * -30", "Math.cos(query.life_time * 100) * -30"], "28.0": ["5+<PERSON>.cos(query.life_time * 180.0) *20", 0, "Math.cos(query.life_time * 180.0) *20"], "28.5667": [5, 0, 0], "29.0": [30, 0, 0]}}, "body3": {"rotation": {"0.0": [0, 0, 0], "1.4833": [0, 0, 0], "9.1667": [0, 0, 0], "10.5667": ["Math.cos(query.anim_time * -40 -70) *-20.0", "Math.cos(query.life_time * 100) * -30", "Math.cos(query.life_time * 100) * -30"], "28.0": ["Math.cos(query.life_time * 180.0) *20", 0, "Math.cos(query.life_time * 180.0) *20"], "28.5167": [0, 0, 0], "30.0": [25, 0, 0]}}, "leftarm": {"rotation": {"0.0": [0, 0, 0], "0.5167": [-60, 0, 0], "1.0": [-60, 0, "-25+<PERSON>.cos(query.life_time * 180.0) *-5"], "27.7167": [-50.52, 0, "-25+<PERSON>.cos(query.life_time * 180.0) *-5"], "28.0": [-10, 0, "-25+<PERSON>.cos(query.life_time * 180.0) *-5"], "29.0": [45, 0, "-25+<PERSON>.cos(query.life_time * 180.0) *-5"]}}, "leftarm2": {"rotation": {"0.0": [0, 0, 0], "0.5167": [-70, 0, 0], "1.0": [-125, 10, 0], "10.5667": [-128.92, 10, 0], "27.7167": [-140.22, 10, 0], "28.0": [-115, 10, 0], "28.5167": [-30, 10, 0]}}, "leftarm3": {"rotation": {"0.0": [-80, 35, -80], "1.0": [-80, 125, -80]}}, "leftarm4": {"rotation": [13, 0, -26]}, "leftarm9": {"rotation": [0, 0, -59]}, "leftarm5": {"rotation": {"0.0": [-2, 0, -25], "1.0": [-2, 0, -10]}}, "leftarm10": {"rotation": [0, 0, -58]}, "leftarm6": {"rotation": {"0.0": [-6, 0, -32], "1.0": [-6, 0, -17]}}, "leftarm11": {"rotation": [0, 0, -50]}, "leftarm7": {"rotation": {"0.0": [-15, 0, -31], "1.0": [-15, 0, -21]}}, "leftarm12": {"rotation": [0, 0, -46]}, "leftarm8": {"rotation": [-15, 0, -34]}, "leftarm13": {"rotation": [0, 0, -41]}, "rightarm": {"rotation": {"0.0": [0, 0, 0], "0.5167": [-60, 0, 0], "1.0": [-70, 0, "25+<PERSON>.cos(query.life_time * 180.0) *5"], "10.5667": [-65, 0, "25+<PERSON>.cos(query.life_time * 180.0) *5"], "27.7167": [-45.88, 0, "25+<PERSON>.cos(query.life_time * 180.0) *5"], "28.0": [5, 0, "25+<PERSON>.cos(query.life_time * 180.0) *5"], "29.0": [55, 0, "25+<PERSON>.cos(query.life_time * 180.0) *5"]}}, "rightarm2": {"rotation": {"0.0": [0, 0, 0], "0.5167": [-65, 0, 0], "1.0": [-130, -10, 15], "10.5667": [-128.23, -10, 9.69], "27.7167": [-135.05, -10, 15.16], "28.0": [-125, -10, 0], "28.5167": [-50, -10, 0]}}, "rightarm3": {"rotation": {"0.0": [-75, -35, 80], "0.4833": [-75, -35, 80], "1.0": [-70, -140, 80], "28.5167": [-75, -95, 80]}}, "rightarm4": {"rotation": {"0.0": [10, 0, 30], "1.0": [30, 0, -10]}}, "rightarm5": {"rotation": [0, 0, 54]}, "rightarm6": {"rotation": {"0.0": [0, 0, 33], "1.0": [0, 0, 3]}}, "rightarm7": {"rotation": [0, 0, 45]}, "rightarm8": {"rotation": {"0.0": [-7, 0, 40], "1.0": [-7, 0, 10]}}, "rightarm9": {"rotation": [0, 0, 41]}, "rightarm10": {"rotation": {"0.0": [-18, 0, 46], "1.0": [-18, 0, 16]}}, "rightarm11": {"rotation": [0, 0, 54]}, "rightarm12": {"rotation": {"0.0": [-20, 0, 47], "1.0": [-20, 0, 22]}}, "rightarm13": {"rotation": [0, 0, 35]}, "neck": {"rotation": {"1.0": [10, 0, 0], "1.4833": [30, 0, 0], "2.0": [30, 0, "Math.cos(query.life_time * 180.0) *5"], "9.1667": [30, 0, "Math.cos(query.life_time * 180.0) *20"], "28.0": [30, 0, "Math.cos(query.life_time * 180.0) *20"], "28.5167": [15, 0, 0], "29.0": [-10, 0, 0], "30.0": [-35, 0, 0]}, "scale": 0.8}, "head": {"rotation": {"0.4833": [25, 0, 0], "1.0": [15, 0, 0], "1.4833": [-30, 0, 0], "9.1667": [-20, 0, 0], "28.5167": [-10, 0, 0], "28.5667": [-20, 0, 0]}}, "jaw": {"rotation": {"1.1167": [0, 0, 0], "9.1667": [0, 0, 0], "10.5667": [0, 0, 0], "24.0": [0, 0, 0], "27.8833": [0, 0, 0], "28.5667": [0, 0, 0]}, "scale": 1}, "leftleg": {"rotation": {"0.0": [0, 0, 0], "1.0": [-35, 0, 0], "8.5167": [-35, 0, 0], "9.1667": [-5, 0, 0]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -5, 0], "8.5167": [0, -5, 0], "9.1667": [0, 0, 0]}}, "leftleg2": {"rotation": {"0.0": [0, 0, 0], "1.0": [90, 0, 0], "8.5167": [90, 0, 0], "9.1667": [5, 0, 0]}}, "leftleg3": {"rotation": {"0.0": [0, 0, 0], "1.0": [-55, 0, 0], "8.5167": [-55, 0, 0], "9.1667": [0, 0, 0]}}, "rightleg": {"rotation": {"0.0": [0, 0, 0], "1.0": [-35, 0, 0], "8.5167": [-35, 0, 0], "9.1667": [-5, 0, 0]}, "position": {"0.0": [0, 0, 0], "1.0": [0, -5, 0], "8.5167": [0, -5, 0], "9.1667": [0, 0, 0]}}, "rightleg2": {"rotation": {"0.0": [0, 0, 0], "1.0": [90, 0, 0], "8.5167": [90, 0, 0], "9.1667": [5, 0, 0]}}, "rightleg3": {"rotation": {"0.0": [0, 0, 0], "1.0": [-50, 0, 0], "8.5167": [-50, 0, 0], "9.1667": [0, 0, 0]}}, "bone": {"rotation": [-10, 0, 0], "position": [0, 0, 0]}, "bone2": {"rotation": [0, 0, 0]}}, "sound_effects": {"5.1667": {"effect": "lookat"}}}, "animation.ithiefi_ditsh_scp096.attack": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"body": {"rotation": {"0.0": [0, "Math.cos(query.life_time * 180.0) *3", 0], "0.1667": [50, 0, 0], "0.3667": [55, 0, 0], "0.5": [0, "Math.cos(query.life_time * 180.0) *3", 0]}, "position": {"0.0": [0, "-3+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0], "0.1667": [0, -6, 0], "0.5": [0, "-3+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0]}}, "body2": {"rotation": {"0.0": ["10+<PERSON>.cos(query.life_time * 180.0) *3", 0, 0], "0.1667": [20, 0, 0], "0.3667": [35, 0, 0], "0.5": ["10+<PERSON>.cos(query.life_time * 180.0) *3", 0, 0]}}, "body3": {"rotation": {"0.0": [35, 0, 0], "0.1667": [-20, 0, 0], "0.5": [35, 0, 0]}}, "leftarm": {"rotation": {"0.0": ["-50+<PERSON>.cos(query.life_time * 180.0) *-3", -5, "Math.cos(query.life_time * 180.0) *-3"], "0.1667": [-125, 0, 0], "0.3667": [-55, 0, 0], "0.5": ["-50+<PERSON>.cos(query.life_time * 180.0) *-3", -5, "Math.cos(query.life_time * 180.0) *-3"]}}, "leftarm2": {"rotation": {"0.0": [-15, 0, 0], "0.1667": [-15, 0, 0], "0.5": [-15, 0, 0]}}, "leftarm3": {"rotation": {"0.0": [0, 95, 0], "0.1667": [-90, 35, -90], "0.3667": [-90, -15, -90], "0.5": [0, 95, 0]}}, "leftarm4": {"rotation": {"0.0": [0, 0, "95+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [20, 0, 10], "0.5": [0, 0, "95+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "leftarm9": {"rotation": {"0.0": [0, 0, 50], "0.1667": [0, 0, 20], "0.5": [0, 0, 50]}}, "leftarm5": {"rotation": {"0.0": [0, 0, "70+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [15, 0, 10], "0.5": [0, 0, "70+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "leftarm10": {"rotation": {"0.0": [0, 0, 35], "0.1667": [0, 0, 20], "0.5": [0, 0, 35]}}, "leftarm6": {"rotation": {"0.0": [0, 0, "40+Math.cos(query.life_time * 180.0) *-3"], "0.1667": [0, 0, 10], "0.5": [0, 0, "40+Math.cos(query.life_time * 180.0) *-3"]}}, "leftarm11": {"rotation": {"0.0": [0, 0, 40], "0.1667": [0, 0, 15], "0.5": [0, 0, 40]}}, "leftarm7": {"rotation": {"0.0": [0, 0, "25+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [-20, 0, 5], "0.5": [0, 0, "25+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "leftarm12": {"rotation": {"0.0": [0, 0, 45], "0.1667": [0, 0, 15], "0.5": [0, 0, 45]}}, "leftarm8": {"rotation": {"0.0": [0, 0, "25+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [-20, 0, 15], "0.5": [0, 0, "25+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "leftarm13": {"rotation": {"0.0": [0, 0, 45], "0.1667": [0, 0, 15], "0.5": [0, 0, 45]}}, "rightarm": {"rotation": {"0.0": ["-50+<PERSON>.cos(query.life_time * 180.0) *3", 5, "Math.cos(query.life_time * 180.0) *3"], "0.1667": [-125, 0, 0], "0.3667": [-50, 0, 0], "0.5": ["-50+<PERSON>.cos(query.life_time * 180.0) *3", 5, "Math.cos(query.life_time * 180.0) *3"]}}, "rightarm2": {"rotation": {"0.0": [-15, 0, 0], "0.1667": [-15, 0, 0], "0.5": [-15, 0, 0]}}, "rightarm3": {"rotation": {"0.0": [0, -95, 0], "0.1667": [-90, -35, 90], "0.3667": [-90, 15, 90], "0.5": [0, -95, 0]}}, "rightarm4": {"rotation": {"0.0": [0, 0, "-60+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [20, 0, -5], "0.5": [0, 0, "-60+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "rightarm5": {"rotation": {"0.0": [0, 0, -45], "0.1667": [0, 0, -20], "0.5": [0, 0, -45]}}, "rightarm6": {"rotation": {"0.0": [0, 0, "-35+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [5, 0, 0], "0.5": [0, 0, "-35+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "rightarm7": {"rotation": {"0.0": [0, 0, -40], "0.1667": [0, 0, -10], "0.5": [0, 0, -40]}}, "rightarm8": {"rotation": {"0.0": [0, 0, "-20+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [-10, 0, 0], "0.5": [0, 0, "-20+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "rightarm9": {"rotation": {"0.0": [0, 0, -30], "0.1667": [0, 0, -15], "0.5": [0, 0, -30]}}, "rightarm10": {"rotation": {"0.0": [0, 0, "-5+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [-20, 0, 0], "0.5": [0, 0, "-5+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "rightarm11": {"rotation": {"0.0": [0, 0, -35], "0.1667": [0, 0, -20], "0.5": [0, 0, -35]}}, "rightarm12": {"rotation": {"0.0": [0, 0, "-30+<PERSON>.cos(query.life_time * 180.0) *-3"], "0.1667": [-15, 0, 0], "0.5": [0, 0, "-30+<PERSON>.cos(query.life_time * 180.0) *-3"]}}, "rightarm13": {"rotation": {"0.0": [0, 0, -50], "0.1667": [0, 0, -25], "0.5": [0, 0, -50]}}, "neck": {"rotation": {"0.0": [-10, 0, 0], "0.1667": [-10, 0, 0], "0.5": [-10, 0, 0]}}, "head": {"rotation": {"0.0": ["-40+query.target_x_rotation - this", "query.target_y_rotation - this", "Math.cos(query.life_time * 180.0) *3"], "0.1667": [-35, 0, 0], "0.5": ["-40+query.target_x_rotation - this", "query.target_y_rotation - this", "Math.cos(query.life_time * 180.0) *3"]}}, "jaw": {"rotation": {"0.0": ["1+Math.cos(query.life_time * 180.0) *-1", 0, 0], "0.5": ["1+Math.cos(query.life_time * 180.0) *-1", 0, 0]}}, "leftleg": {"rotation": {"0.0": [-35, 0, 0], "0.1667": [-50, 0, 0], "0.3667": [-50, 0, 0], "0.5": [-35, 0, 0]}, "position": {"0.0": [0, "-3.3+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0], "0.1667": [0, -6, 0], "0.5": [0, "-3.3+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0]}}, "leftleg2": {"rotation": {"0.0": [70, 0, 0], "0.1667": [90, 0, 0], "0.3667": [90, 0, 0], "0.5": [70, 0, 0]}}, "leftleg3": {"rotation": {"0.0": [-35, 0, 0], "0.1667": [-41, 0, 0], "0.3667": [-40, 0, 0], "0.5": [-35, 0, 0]}}, "rightleg": {"rotation": {"0.0": [-35, 0, 0], "0.1667": [-50, 0, 0], "0.3667": [-50, 0, 0], "0.5": [-35, 0, 0]}, "position": {"0.0": [0, "-3.3+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0], "0.1667": [0, -6, 0], "0.5": [0, "-3.3+<PERSON>.cos(query.life_time * 180.0) *-0.3", 0]}}, "rightleg2": {"rotation": {"0.0": [70, 0, 0], "0.1667": [95, 0, 0], "0.3667": [90, 0, 0], "0.5": [70, 0, 0]}}, "rightleg3": {"rotation": {"0.0": [-35, 0, 0], "0.1667": [-41, 0, 0], "0.3667": [-40, 0, 0], "0.5": [-35, 0, 0]}}}}}}