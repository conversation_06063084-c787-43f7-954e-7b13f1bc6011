{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:sonic", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}}}, "component_groups": {"ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 13, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}, "ditsh:speed_tier_1": {"minecraft:movement": {"value": 0.22}, "minecraft:behavior.timer_flag_2": {"priority": 9, "cooldown_range": 0, "duration_range": 20, "on_end": {"event": "ditsh:speed_tier_2_event", "target": "self"}}}, "ditsh:speed_tier_2": {"minecraft:movement": {"value": 0.24}, "minecraft:behavior.timer_flag_2": {"priority": 9, "cooldown_range": 0, "duration_range": 20, "on_end": {"event": "ditsh:speed_tier_3_event", "target": "self"}}}, "ditsh:speed_tier_3": {"minecraft:movement": {"value": 0.26}, "minecraft:behavior.timer_flag_2": {"priority": 9, "cooldown_range": 0, "duration_range": 20, "on_end": {"event": "ditsh:speed_tier_4_event", "target": "self"}}}, "ditsh:speed_tier_4": {"minecraft:movement": {"value": 0.28}, "minecraft:behavior.timer_flag_2": {"priority": 9, "cooldown_range": 0, "duration_range": 20, "on_end": {"event": "ditsh:speed_tier_5_event", "target": "self"}}}, "ditsh:speed_tier_5": {"minecraft:movement": {"value": 0.3}, "minecraft:behavior.timer_flag_2": {"priority": 9, "cooldown_range": 0, "duration_range": 20, "on_end": {"event": "ditsh:speed_tier_6_event", "target": "self"}}}, "ditsh:speed_tier_6": {"minecraft:movement": {"value": 0.32}, "minecraft:behavior.timer_flag_2": {"priority": 9, "cooldown_range": 0, "duration_range": 20, "on_end": {"event": "ditsh:speed_tier_7_event", "target": "self"}}}, "ditsh:speed_tier_7": {"minecraft:movement": {"value": 0.34}}}, "events": {"minecraft:entity_spawned": {"queue_command": {"command": "playsound mob.ditsh.sonic.spawn @a ~ ~ ~"}, "add": {"component_groups": ["ditsh:speed_tier_1"]}}, "ditsh:on_death": {}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}, "ditsh:on_kill": {"queue_command": {"command": "playsound mob.ditsh.sonic.kill @a ~ ~ ~"}}, "ditsh:speed_tier_2_event": {"remove": {"component_groups": ["ditsh:speed_tier_1"]}, "add": {"component_groups": ["ditsh:speed_tier_2"]}}, "ditsh:speed_tier_3_event": {"remove": {"component_groups": ["ditsh:speed_tier_2"]}, "add": {"component_groups": ["ditsh:speed_tier_3"]}}, "ditsh:speed_tier_4_event": {"remove": {"component_groups": ["ditsh:speed_tier_3"]}, "add": {"component_groups": ["ditsh:speed_tier_4"]}}, "ditsh:speed_tier_5_event": {"remove": {"component_groups": ["ditsh:speed_tier_4"]}, "add": {"component_groups": ["ditsh:speed_tier_5"]}}, "ditsh:speed_tier_6_event": {"remove": {"component_groups": ["ditsh:speed_tier_5"]}, "add": {"component_groups": ["ditsh:speed_tier_6"]}}, "ditsh:speed_tier_7_event": {"remove": {"component_groups": ["ditsh:speed_tier_6"]}, "add": {"component_groups": ["ditsh:speed_tier_7"]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "sonic", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.5, "height": 1.7}, "minecraft:health": {"value": 50, "max": 50}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:movement": {"value": 0.2}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 10}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.2, "on_kill": {"event": "ditsh:on_kill", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:environment_sensor": {"triggers": [{"event": "ditsh:start_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}}, {"event": "ditsh:stop_chase_music", "filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": true}]}}]}}}}