{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ithiefi_ditsh_scp049", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 3, "visible_bounds_height": 3, "visible_bounds_offset": [0, 1.5, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "torso", "parent": "root", "pivot": [0, 14, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}, {"origin": [-4, 14, -3], "size": [8, 10, 6], "uv": [14, 16]}, {"origin": [-8, 21, -3], "size": [16, 3, 6], "inflate": 0.3, "uv": [12, 15]}]}, {"name": "head", "parent": "torso", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "inflate": 0.5, "uv": [32, 0]}, {"origin": [-2.5, 27.5, -4.8], "size": [2, 2, 2], "inflate": -0.7, "uv": [0, 53]}, {"origin": [0.5, 27.5, -4.8], "size": [2, 2, 2], "inflate": -0.7, "uv": [0, 53]}, {"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "head2", "parent": "head", "pivot": [0, 26.5, -3.5], "rotation": [25, 0, 0], "cubes": [{"origin": [-2, 25, -5], "size": [4, 3, 2], "uv": [0, 59]}]}, {"name": "head3", "parent": "head", "pivot": [0, 25.5, -4.5], "rotation": [30, 0, 0], "cubes": [{"origin": [-2, 24, -6], "size": [4, 3, 2], "inflate": -0.3, "uv": [0, 59]}]}, {"name": "head4", "parent": "head", "pivot": [0, 25.5, -5], "rotation": [45, 0, 0], "cubes": [{"origin": [-2, 23.5, -8], "size": [4, 3, 3], "inflate": -0.6, "uv": [0, 59]}]}, {"name": "head5", "parent": "head", "pivot": [0, 24, -6], "rotation": [65, 0, 0], "cubes": [{"origin": [-2, 22.5, -9], "size": [4, 3, 4], "inflate": -0.8, "uv": [0, 57]}]}, {"name": "hoody", "parent": "head", "pivot": [0, 32, 0]}, {"name": "hood4", "parent": "hoody", "pivot": [4, 32.5, 4], "rotation": [-85, 0, 0], "cubes": [{"origin": [-4, 32, 4], "size": [8, 1, 9], "uv": [1, 31]}]}, {"name": "hood3", "parent": "hoody", "pivot": [4, 32.5, -5], "rotation": [-85, 90, 0], "cubes": [{"origin": [-4, 32, -5], "size": [8, 1, 9], "uv": [1, 31]}]}, {"name": "hood2", "parent": "hoody", "pivot": [-4, 32.5, -5], "rotation": [-85, 90, 10], "cubes": [{"origin": [-12, 32, -5], "size": [8, 1, 9], "uv": [1, 31]}]}, {"name": "hood", "parent": "hoody", "pivot": [0, 32.5, 0], "cubes": [{"origin": [-4, 32, -4.1], "size": [8, 1, 8.1], "inflate": 0.5, "uv": [0, 32]}]}, {"name": "rightArm", "parent": "torso", "pivot": [-5, 22, 0], "cubes": [{"origin": [-8, 18, -2], "size": [4, 6, 4], "uv": [32, 48]}, {"origin": [-7, 17, -2], "size": [3, 2, 4], "uv": [32, 52]}]}, {"name": "rightItem", "parent": "rightArm", "pivot": [-6, 15, 1]}, {"name": "rightArm2", "parent": "rightArm", "pivot": [-6, 18, 0], "cubes": [{"origin": [-8, 12, -2], "size": [4, 6, 4], "inflate": 0.001, "uv": [32, 54]}, {"origin": [-7, 17, -2], "size": [3, 2, 4], "uv": [32, 52]}]}, {"name": "rightItem2", "parent": "rightArm2", "pivot": [-6, 15, 1]}, {"name": "leftArm", "parent": "torso", "pivot": [5, 22, 0], "mirror": true, "cubes": [{"origin": [4, 18, -2], "size": [4, 6, 4], "uv": [40, 16], "mirror": false}, {"origin": [4, 17, -2], "size": [3, 2, 4], "uv": [40, 20], "mirror": false}]}, {"name": "leftArm2", "parent": "leftArm", "pivot": [6, 18, 0], "mirror": true, "cubes": [{"origin": [4, 12, -2], "size": [4, 6, 4], "inflate": 0.001, "uv": [40, 22], "mirror": false}, {"origin": [4, 17, -2], "size": [3, 2, 4], "uv": [40, 22], "mirror": false}]}, {"name": "waist", "parent": "root", "pivot": [0, 12, 0]}, {"name": "rightLeg", "parent": "waist", "pivot": [-1.9, 12, 0], "cubes": [{"origin": [-3.9, 6, -2], "size": [4, 6, 4], "uv": [16, 48]}, {"origin": [-3.9, 5, -2], "size": [4, 2, 4], "uv": [16, 52]}]}, {"name": "rightLeg2", "parent": "rightLeg", "pivot": [-1.9, 6, 0], "cubes": [{"origin": [-3.9, 0, -2], "size": [4, 6, 4], "uv": [16, 54]}, {"origin": [-3.9, 5, -2], "size": [4, 2, 4], "uv": [16, 54]}]}, {"name": "leftLeg", "parent": "waist", "pivot": [1.9, 12, 0], "mirror": true, "cubes": [{"origin": [-0.1, 6, -2], "size": [4, 6, 4], "uv": [0, 16], "mirror": false}, {"origin": [-0.1, 5, -2], "size": [4, 2, 4], "uv": [0, 20], "mirror": false}]}, {"name": "leftLeg2", "parent": "leftLeg", "pivot": [1.9, 6, 0], "mirror": true, "cubes": [{"origin": [-0.1, 0, -2], "size": [4, 6, 4], "uv": [0, 22], "mirror": false}, {"origin": [-0.1, 5, -2], "size": [4, 2, 4], "uv": [0, 22], "mirror": false}]}, {"name": "body2", "parent": "waist", "pivot": [0, 13.9, 0], "cubes": [{"origin": [-4, 5, -3], "size": [8, 9, 6], "inflate": 0.1, "uv": [36, 32]}]}]}]}